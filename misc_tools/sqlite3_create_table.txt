-- bond_repo_trade_record Table
CREATE TABLE bond_repo_trade_record (
    index_no TEXT,
    fund_name TEXT,
    as_date TEXT,  -- SQLite stores dates as TEXT
    settle_date TEXT,
    repo_termination_date TEXT,
    trade_ticket TEXT,
    trade_account TEXT,
    trade_direction TEXT,
    isin_code TEXT,
    sec_desc TEXT,
    trade_currency TEXT,
    haircut_fractional_indicator REAL,
    repo_rate REAL,
    trade_amount REAL,
    trade_price REAL,
    repo_loan_amount REAL,
    accrued_interest REAL,
    settle_total REAL,
    termination_money REAL,
    level1_tag_name TEXT,
    firm_broker TEXT,
    trade_type TEXT,
    repo_term_type TEXT,
    repo_status TEXT,
    settle_location TEXT
);

-- bond_trade_record Table
CREATE TABLE bond_trade_record (
    index_no TEXT,
    fund_name TEXT,
    as_date TEXT,
    settle_date TEXT,
    trade_ticket TEXT,
    trade_account TEXT,
    trade_direction TEXT,
    isin_code TEXT,
    sec_desc TEXT,
    trade_currency TEXT,
    trade_amount REAL,
    trade_price REAL,
    accrued_interest REAL,
    settle_total REAL,
    level1_tag_name TEXT,
    firm_broker TEXT,
    trade_type TEXT,
    settle_location TEXT,
    trade_yield REAL
);

-- bond_trade_record_mbs Table
CREATE TABLE bond_trade_record_mbs (
    index_no TEXT,
    fund_name TEXT,
    as_date TEXT,
    settle_date TEXT,
    trade_ticket TEXT,
    trade_account TEXT,
    trade_direction TEXT,
    isin_code TEXT,
    sec_desc TEXT,
    trade_currency TEXT,
    trade_amount REAL,
    trade_price REAL,
    accrued_interest REAL,
    settle_total REAL,
    level1_tag_name TEXT,
    firm_broker TEXT,
    trade_type TEXT,
    settle_location TEXT,
    trade_yield REAL
);
