USE [master]
GO
/****** Object:  Database [FMSDB]    Script Date: 2025/1/14 16:47:08 ******/
CREATE DATABASE [FMSDB]
 CONTAINMENT = NONE
 ON  PRIMARY 
( NAME = N'FMSDB', FILENAME = N'D:\DB\FMSDB\FMSDB.mdf' , SIZE = 5120KB , MAXSIZE = UNLIMITED, FILEGROWTH = 1024KB )
 LOG ON 
( NAME = N'FMSDB_log', FILENAME = N'D:\DB\FMSDB\FMSDB_log.ldf' , SIZE = 2048KB , MAXSIZE = 2048GB , FILEGROWTH = 10%)
GO
ALTER DATABASE [FMSDB] SET COMPATIBILITY_LEVEL = 120
GO
IF (1 = FULLTEXTSERVICEPROPERTY('IsFullTextInstalled'))
begin
EXEC [FMSDB].[dbo].[sp_fulltext_database] @action = 'enable'
end
GO
ALTER DATABASE [FMSDB] SET ANSI_NULL_DEFAULT OFF 
GO
ALTER DATABASE [FMSDB] SET ANSI_NULLS OFF 
GO
ALTER DATABASE [FMSDB] SET ANSI_PADDING OFF 
GO
ALTER DATABASE [FMSDB] SET ANSI_WARNINGS OFF 
GO
ALTER DATABASE [FMSDB] SET ARITHABORT OFF 
GO
ALTER DATABASE [FMSDB] SET AUTO_CLOSE OFF 
GO
ALTER DATABASE [FMSDB] SET AUTO_SHRINK ON 
GO
ALTER DATABASE [FMSDB] SET AUTO_UPDATE_STATISTICS ON 
GO
ALTER DATABASE [FMSDB] SET CURSOR_CLOSE_ON_COMMIT OFF 
GO
ALTER DATABASE [FMSDB] SET CURSOR_DEFAULT  GLOBAL 
GO
ALTER DATABASE [FMSDB] SET CONCAT_NULL_YIELDS_NULL OFF 
GO
ALTER DATABASE [FMSDB] SET NUMERIC_ROUNDABORT OFF 
GO
ALTER DATABASE [FMSDB] SET QUOTED_IDENTIFIER OFF 
GO
ALTER DATABASE [FMSDB] SET RECURSIVE_TRIGGERS OFF 
GO
ALTER DATABASE [FMSDB] SET  DISABLE_BROKER 
GO
ALTER DATABASE [FMSDB] SET AUTO_UPDATE_STATISTICS_ASYNC OFF 
GO
ALTER DATABASE [FMSDB] SET DATE_CORRELATION_OPTIMIZATION OFF 
GO
ALTER DATABASE [FMSDB] SET TRUSTWORTHY OFF 
GO
ALTER DATABASE [FMSDB] SET ALLOW_SNAPSHOT_ISOLATION OFF 
GO
ALTER DATABASE [FMSDB] SET PARAMETERIZATION SIMPLE 
GO
ALTER DATABASE [FMSDB] SET READ_COMMITTED_SNAPSHOT OFF 
GO
ALTER DATABASE [FMSDB] SET HONOR_BROKER_PRIORITY OFF 
GO
ALTER DATABASE [FMSDB] SET RECOVERY SIMPLE 
GO
ALTER DATABASE [FMSDB] SET  MULTI_USER 
GO
ALTER DATABASE [FMSDB] SET PAGE_VERIFY CHECKSUM  
GO
ALTER DATABASE [FMSDB] SET DB_CHAINING OFF 
GO
ALTER DATABASE [FMSDB] SET FILESTREAM( NON_TRANSACTED_ACCESS = OFF ) 
GO
ALTER DATABASE [FMSDB] SET TARGET_RECOVERY_TIME = 0 SECONDS 
GO
ALTER DATABASE [FMSDB] SET DELAYED_DURABILITY = DISABLED 
GO
USE [FMSDB]
GO
/****** Object:  User [fms_admin]    Script Date: 2025/1/14 16:47:08 ******/
CREATE USER [fms_admin] FOR LOGIN [fms_admin] WITH DEFAULT_SCHEMA=[dbo]
GO
ALTER ROLE [db_owner] ADD MEMBER [fms_admin]
GO
/****** Object:  Table [dbo].[bond_repo_trade_record]    Script Date: 2025/1/14 16:47:08 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[bond_repo_trade_record](
	[index_no] [nvarchar](50) NULL,
	[fund_name] [nvarchar](255) NULL,
	[as_date] [date] NULL,
	[settle_date] [date] NULL,
	[repo_termination_date] [date] NULL,
	[trade_ticket] [nvarchar](50) NULL,
	[trade_account] [nvarchar](50) NULL,
	[trade_direction] [nvarchar](50) NULL,
	[isin_code] [nvarchar](50) NULL,
	[sec_desc] [nvarchar](255) NULL,
	[trade_currency] [nvarchar](50) NULL,
	[haircut_fractional_indicator] [float] NULL,
	[repo_rate] [float] NULL,
	[trade_amount] [float] NULL,
	[trade_price] [float] NULL,
	[repo_loan_amount] [float] NULL,
	[accrued_interest] [float] NULL,
	[settle_total] [float] NULL,
	[termination_money] [float] NULL,
	[level1_tag_name] [nvarchar](50) NULL,
	[firm_broker] [nvarchar](50) NULL,
	[trade_type] [nvarchar](50) NULL,
	[repo_term_type] [nvarchar](50) NULL,
	[repo_status] [nvarchar](50) NULL,
	[settle_location] [nvarchar](50) NULL
) ON [PRIMARY]

GO
/****** Object:  Table [dbo].[bond_trade_record]    Script Date: 2025/1/14 16:47:08 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[bond_trade_record](
	[index_no] [nvarchar](50) NULL,
	[fund_name] [nvarchar](255) NULL,
	[as_date] [date] NULL,
	[settle_date] [date] NULL,
	[trade_ticket] [nvarchar](50) NULL,
	[trade_account] [nvarchar](50) NULL,
	[trade_direction] [nvarchar](50) NULL,
	[isin_code] [nvarchar](50) NULL,
	[sec_desc] [nvarchar](255) NULL,
	[trade_currency] [nvarchar](50) NULL,
	[trade_amount] [float] NULL,
	[trade_price] [float] NULL,
	[accrued_interest] [float] NULL,
	[settle_total] [float] NULL,
	[level1_tag_name] [nvarchar](50) NULL,
	[firm_broker] [nvarchar](50) NULL,
	[trade_type] [nvarchar](50) NULL,
	[settle_location] [nvarchar](50) NULL,
	[trade_yield] [float] NULL
) ON [PRIMARY]

GO
/****** Object:  Table [dbo].[bond_trade_record_mbs]    Script Date: 2025/1/14 16:47:08 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[bond_trade_record_mbs](
	[index_no] [nvarchar](50) NULL,
	[fund_name] [nvarchar](255) NULL,
	[as_date] [date] NULL,
	[settle_date] [date] NULL,
	[trade_ticket] [nvarchar](50) NULL,
	[trade_account] [nvarchar](50) NULL,
	[trade_direction] [nvarchar](50) NULL,
	[isin_code] [nvarchar](50) NULL,
	[sec_desc] [nvarchar](255) NULL,
	[trade_currency] [nvarchar](50) NULL,
	[trade_amount] [float] NULL,
	[trade_price] [float] NULL,
	[accrued_interest] [float] NULL,
	[settle_total] [float] NULL,
	[level1_tag_name] [nvarchar](50) NULL,
	[firm_broker] [nvarchar](50) NULL,
	[trade_type] [nvarchar](50) NULL,
	[settle_location] [nvarchar](50) NULL,
	[trade_yield] [float] NULL
) ON [PRIMARY]

GO
USE [master]
GO
ALTER DATABASE [FMSDB] SET  READ_WRITE 
GO
