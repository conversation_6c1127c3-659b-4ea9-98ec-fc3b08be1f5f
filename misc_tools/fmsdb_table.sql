USE [FMSDB]
GO
/****** Object:  Table [dbo].[bond_repo_trade_record]    Script Date: 2025/1/14 16:47:37 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[bond_repo_trade_record](
	[index_no] [nvarchar](50) NULL,
	[fund_name] [nvarchar](255) NULL,
	[as_date] [date] NULL,
	[settle_date] [date] NULL,
	[repo_termination_date] [date] NULL,
	[trade_ticket] [nvarchar](50) NULL,
	[trade_account] [nvarchar](50) NULL,
	[trade_direction] [nvarchar](50) NULL,
	[isin_code] [nvarchar](50) NULL,
	[sec_desc] [nvarchar](255) NULL,
	[trade_currency] [nvarchar](50) NULL,
	[haircut_fractional_indicator] [float] NULL,
	[repo_rate] [float] NULL,
	[trade_amount] [float] NULL,
	[trade_price] [float] NULL,
	[repo_loan_amount] [float] NULL,
	[accrued_interest] [float] NULL,
	[settle_total] [float] NULL,
	[termination_money] [float] NULL,
	[level1_tag_name] [nvarchar](50) NULL,
	[firm_broker] [nvarchar](50) NULL,
	[trade_type] [nvarchar](50) NULL,
	[repo_term_type] [nvarchar](50) NULL,
	[repo_status] [nvarchar](50) NULL,
	[settle_location] [nvarchar](50) NULL
) ON [PRIMARY]

GO
/****** Object:  Table [dbo].[bond_trade_record]    Script Date: 2025/1/14 16:47:37 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[bond_trade_record](
	[index_no] [nvarchar](50) NULL,
	[fund_name] [nvarchar](255) NULL,
	[as_date] [date] NULL,
	[settle_date] [date] NULL,
	[trade_ticket] [nvarchar](50) NULL,
	[trade_account] [nvarchar](50) NULL,
	[trade_direction] [nvarchar](50) NULL,
	[isin_code] [nvarchar](50) NULL,
	[sec_desc] [nvarchar](255) NULL,
	[trade_currency] [nvarchar](50) NULL,
	[trade_amount] [float] NULL,
	[trade_price] [float] NULL,
	[accrued_interest] [float] NULL,
	[settle_total] [float] NULL,
	[level1_tag_name] [nvarchar](50) NULL,
	[firm_broker] [nvarchar](50) NULL,
	[trade_type] [nvarchar](50) NULL,
	[settle_location] [nvarchar](50) NULL,
	[trade_yield] [float] NULL
) ON [PRIMARY]

GO
/****** Object:  Table [dbo].[bond_trade_record_mbs]    Script Date: 2025/1/14 16:47:37 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[bond_trade_record_mbs](
	[index_no] [nvarchar](50) NULL,
	[fund_name] [nvarchar](255) NULL,
	[as_date] [date] NULL,
	[settle_date] [date] NULL,
	[trade_ticket] [nvarchar](50) NULL,
	[trade_account] [nvarchar](50) NULL,
	[trade_direction] [nvarchar](50) NULL,
	[isin_code] [nvarchar](50) NULL,
	[sec_desc] [nvarchar](255) NULL,
	[trade_currency] [nvarchar](50) NULL,
	[trade_amount] [float] NULL,
	[trade_price] [float] NULL,
	[accrued_interest] [float] NULL,
	[settle_total] [float] NULL,
	[level1_tag_name] [nvarchar](50) NULL,
	[firm_broker] [nvarchar](50) NULL,
	[trade_type] [nvarchar](50) NULL,
	[settle_location] [nvarchar](50) NULL,
	[trade_yield] [float] NULL
) ON [PRIMARY]

GO
