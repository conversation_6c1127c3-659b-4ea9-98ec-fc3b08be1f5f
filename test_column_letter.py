def get_column_letter(col_num):
    """将列索引转换为Excel列字母 (1=A, 2=B, ..., 26=Z, 27=AA, ...)"""
    result = ""
    while col_num > 0:
        col_num -= 1
        result = chr(col_num % 26 + ord('A')) + result
        col_num //= 26
    return result

# 测试函数
test_cases = [1, 2, 26, 27, 52, 702, 703]
for i in test_cases:
    print(f"{i} -> {get_column_letter(i)}")

# 预期结果:
# 1 -> A
# 2 -> B  
# 26 -> Z
# 27 -> AA
# 52 -> AZ
# 702 -> ZZ
# 703 -> AAA
