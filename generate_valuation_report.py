import xlwings
import xlwings.constants
from colorama import init, Fore, Style

from PublicFunc import *

# 初始化colorama
init()

# 定义颜色打印函数
def print_error(message):
    print(Fore.RED + message + Style.RESET_ALL)

def print_warning(message):
    print(Fore.YELLOW + message + Style.RESET_ALL)

def print_success(message):
    print(Fore.GREEN + message + Style.RESET_ALL)

def print_alert(message):
    print(Fore.RED + "⚠️  " + message + Style.RESET_ALL)



# 显示所有列
pd.set_option('display.max_columns', None)

# 或者如果你想要设置最大列数为某个特定值而不是无限制，可以这样做：
# pd.set_option('display.max_columns', 100)  # 例如显示最多100列

# 显示完整的DataFrame内容，不截断
pd.set_option('display.expand_frame_repr', False)

# 如果你还想确保所有的行都显示出来，而不仅仅是前几行和后几行，你可以这样设置：
pd.set_option('display.max_rows', None)


# 算法是这样的，比如df一共有5列'as_date', 'isin_code', 'repo_rate', 'firm_broker', 'status'，现在增加一列'is_alive'，df按照'as_date', 'isin_code', 'repo_rate', 'firm_broker'分组，然后按照下面规则处理：
# 1. 如果每一组中行数等于1，则对应的is_alive就填入YES；
# 2. 如果每一组中行数等于2，并且第一条的status是active，第二条是closed，则不做处理;
# 3. 如果每一组中行数大于等于2，并且所有行的status都是active，则每一行对应的is_alive就填入YES;
# 4. 如果每一组中行数大于2，并且有些行的status是active，有些是closed，那将closed的行按在df中的顺序和active的行在df中的顺序进行一一配对，能配对的就不做处理，剩下的无法配对的active行对应的is_alive填入YES。（注意active行的数量肯定大于等于closed行的数量，所以剩下的必定是active行）
# 我的做法是为了处理债券逆回购的交易，也就是每一条active的记录，会有一条closed记录配对，如果能配对，代表这笔逆回购的结束，否则如果对于active的记录，找不到能与之配对的closed记录，代表这笔逆回购尚未结束，is_alive就是YES。
# repo_trade_record分组处理函数
def process_group(group):
    # 规则1: 如果每组只有一条记录，is_alive 为 'YES'
    if len(group) == 1:
        if not pd.isna(group['repo_term_type'].iloc[0]):
            if group['repo_term_type'].iloc[0].lower() == 'term' and group['repo_termination_date'].iloc[0] <= report_date:
                group['is_alive'] = None
            else:
                group['is_alive'] = 'YES'
        else:
            group['is_alive'] = 'YES'
        return group

    # 规则2: 如果每组有两条记录，且第一条是 active，第二条是 closed，不做处理
    elif len(group) == 2 and group['repo_status'].iloc[0].lower() == 'active' and group['repo_status'].iloc[1].lower() == 'closed':
        group['is_alive'] = None  # 保持原状
        return group

    # 规则2.1: 如果每组有两条记录，且第一条是 closed，第二条是 active，则第一条不做处理，第二条设为YES
    elif len(group) == 2 and group['repo_status'].iloc[0].lower() == 'closed' and group['repo_status'].iloc[1].lower() == 'active':
        # group.loc[0, 'is_alive'] = None
        # group.loc[1, 'is_alive'] = 'YES'
        group['is_alive'] = None  # 保持原状
        return group

    # 规则3: 如果每组所有记录的 status 都是 active，is_alive 都是 'YES'
    elif all(group['repo_status'].str.lower() == 'active'):
        group['is_alive'] = 'YES'
        return group

    # 规则4: 如果有 active 和 closed 的组合
    else:
        # 按 as_date 排序
        group = group.sort_values(by='as_date')

        # 提取 active 和 closed 的行
        active_rows = group[group['repo_status'].str.lower() == 'active']
        closed_rows = group[group['repo_status'].str.lower() == 'closed']

        # 配对过程：按顺序配对 active 和 closed
        paired_active_rows = active_rows.head(len(closed_rows))

        # 对于剩下的没有配对的 active 行，设置 is_alive 为 'YES'
        remaining_active_rows = active_rows.tail(len(active_rows) - len(closed_rows))
        group.loc[remaining_active_rows.index, 'is_alive'] = 'YES'

        # 将配对的 active 行的 is_alive 留空（不做处理）
        group.loc[paired_active_rows.index, 'is_alive'] = None

        return group


def calculate_capital_pl(df):
    capital_pl = []  # 存储每个卖出交易的资本损益
    buy_orders = {}  # 存储不同债券的买入订单，按 ISIN 分组
    # 遍历每笔交易
    for idx, row in df.iterrows():
        isin = row['Ticker']
        if isin == 'XS1063561499':
            pppp = 1
        if row['B/S'] == 'B':
            # 记录买入的数量和价格，按 ISIN 分组
            if isin not in buy_orders:
                buy_orders[isin] = []
            buy_orders[isin].append({'Quantity': row['Quantity'], 'Price': row['买入价格']})
            capital_pl.append(None)  # 对于买入，资本损益为 None
        elif row['B/S'] == 'S':
            # 卖出时，计算资本损益
            sell_quantity = row['Quantity']
            sell_price = row['卖出价格']
            capital_pl_sell = 0
            # 按照 FIFO 规则进行卖出
            while sell_quantity > 0 and buy_orders[isin]:
                # 取最早的买入订单
                buy_order = buy_orders[isin][0]
                # 如果买入数量大于等于卖出数量
                if buy_order['Quantity'] >= sell_quantity:
                    # 计算该部分的资本损益
                    capital_pl_sell += sell_quantity * (sell_price - buy_order['Price'])
                    # 更新买入订单的剩余数量
                    buy_orders[isin][0]['Quantity'] -= sell_quantity
                    sell_quantity = 0
                else:
                    # 卖出数量小于买入数量，完全卖出这个买单
                    capital_pl_sell += buy_order['Quantity'] * (sell_price - buy_order['Price'])
                    sell_quantity -= buy_order['Quantity']
                    # 完全使用了当前买单，移除它
                    buy_orders[isin].pop(0)
            # 将该笔卖出的资本损益添加到列表中
            capital_pl.append(capital_pl_sell)
    # 将资本损益结果添加到原始 DataFrame 中
    df['Capital P/L'] = capital_pl
    return df


def check_na_errors(workbook, na_check_sheet_list, na_sheet, debug=True):
    """
    检查workbook中所有sheet的每个单元格，如果发现内容包含#N/A，
    就记录到一个新的workbook中名为NA_sheet的sheet中

    Args:
        workbook: 要检查的xlwings workbook对象
        na_sheet: 用于记录#N/A错误的sheet
    """

    # 设置表头
    headers = ['Sheet Name', 'Cell Address', 'Cell Value', 'Formula']
    na_sheet.range('A1').value = headers

    # 格式化表头
    na_sheet.range('A1:D1').font.bold = True
    na_sheet.range('A1:D1').color = (192, 192, 192)  # 灰色背景

    error_count = 0
    row_index = 2  # 从第2行开始记录数据

    print(Fore.YELLOW + "Checking for #N/A errors in all sheets..." + Style.RESET_ALL)

    # 遍历workbook中的所有sheet
    for sheet_name in na_check_sheet_list:
        print(f"Checking sheet: {sheet_name}")
        sheet = workbook.sheets[sheet_name]
        if debug:
            print(f"  Sheet has {len(sheet.used_range.value) if sheet.used_range else 0} rows")

        try:
            # 获取sheet的使用范围
            used_range = sheet.used_range
            if used_range is None:
                print(f"  Sheet {sheet.name} is empty, skipping...")
                continue

            # 获取所有数据
            values = used_range.value
            formulas = used_range.formula

            # 如果只有一个单元格，转换为列表格式
            if not isinstance(values, list):
                values = [[values]]
                formulas = [[formulas]] if formulas is not None else [[""]]
            elif values and not isinstance(values[0], list):
                values = [values]
                formulas = [formulas] if formulas is not None else [""]

            # 检查每个单元格
            for row_idx, row_data in enumerate(values):
                if not isinstance(row_data, list):
                    row_data = [row_data]

                for col_idx, cell_value in enumerate(row_data):
                    # 检查单元格值是否包含#N/A
                    if cell_value is not None and str(cell_value).find('#N/A') != -1:
                        # 计算实际的单元格地址（基于used_range的起始位置）
                        actual_row = used_range.row + row_idx
                        actual_col = used_range.column + col_idx
                        cell_address = get_column_letter(actual_col) + str(actual_row)

                        # 获取对应的公式
                        cell_formula = ""
                        try:
                            if formulas and row_idx < len(formulas):
                                if isinstance(formulas[row_idx], list) and col_idx < len(formulas[row_idx]):
                                    cell_formula = formulas[row_idx][col_idx] if formulas[row_idx][col_idx] else ""
                                elif not isinstance(formulas[row_idx], list) and col_idx == 0:
                                    cell_formula = formulas[row_idx] if formulas[row_idx] else ""
                        except Exception as e:
                            cell_formula = f"Error getting formula: {str(e)}"

                        # 记录到NA_sheet
                        na_sheet.range(f'A{row_index}').value = sheet.name
                        na_sheet.range(f'B{row_index}').value = cell_address
                        na_sheet.range(f'C{row_index}').value = str(cell_value)
                        na_sheet.range(f'D{row_index}').value = str(cell_formula)

                        error_count += 1
                        row_index += 1

                        print(Fore.RED + f"Found #N/A error in {sheet.name}!{cell_address}: {cell_value}" + Style.RESET_ALL)

        except Exception as e:
            print(Fore.RED + f"Error checking sheet {sheet.name}: {str(e)}" + Style.RESET_ALL)
            continue

    # 自动调整列宽
    na_sheet.autofit()

    return error_count


if __name__ == '__main__':
    cash = 0
    diff_market_value = 0
    diff_mgt_fee = 0
    custody_fee = 0

    print('Start to generate the valuation report of {}'.format(fund_name))
    print('----------------------------------------')

    while True:
        user_input = input("Please input report date(YYYY-MM-DD): ")
        if not user_input:  # 如果用户没有输入任何内容
            print("No date input. The current date will be used as default date{}".format(dt.datetime.today().strftime('%Y-%m-%d')))
            report_date = dt.datetime.today().strftime('%Y-%m-%d')
            break
        if is_valid_date(user_input):
            report_date = pd.to_datetime(user_input).strftime('%Y-%m-%d')
            break
        else:
            print("Invalid date format. Please enter a valid date.")

    while True:
        user_input = input("Please input the mgt fee date of last phase(YYYY-MM-DD): ")
        if not user_input:  # 如果用户没有输入任何内容
            print("No date input. The end date of last month will be used as default date{}".format(get_last_day_of_previous_month(report_date).strftime('%Y-%m-%d')))
            last_mgt_fee_date = get_last_day_of_previous_month(report_date).strftime('%Y-%m-%d')
            break
        if is_valid_date(user_input):
            last_mgt_fee_date = pd.to_datetime(user_input).strftime('%Y-%m-%d')
            break
        else:
            print("Invalid date format. Please enter a valid date.")

    while True:
        user_input = input("Please input cash market value: ")
        if not user_input:  # 如果用户没有输入任何内容
            print("No data input, The default number 4362480.75 will be used.")
            cash = 4362480.75
            break
        try:
            cash = float(user_input)
            break
        except ValueError:
            print("Invalid date format. Please enter a valid date.")

    while True:
        user_input = input("Please input the diff value of sum of Market value : ")
        if not user_input:  # 如果用户没有输入任何内容
            print("No data input, The default number -241381.004988257 will be used.")
            diff_market_value = -241381.004988257
            break
        try:
            diff_market_value = float(user_input)
            break
        except ValueError:
            print("Invalid date format. Please enter a valid date.")

    while True:
        user_input = input("Please input the diff value of mgt fee: ")
        if not user_input:  # 如果用户没有输入任何内容
            print("No data input, The default number 79995.57 will be used.")
            diff_mgt_fee = 79995.57
            break
        try:
            diff_mgt_fee = float(user_input)
            break
        except ValueError:
            print("Invalid date format. Please enter a valid date.")

    while True:
        user_input = input("Please input custody fee value: ")
        if not user_input:  # 如果用户没有输入任何内容
            print("No data input, The default number 39913.03 will be used.")
            custody_fee = 39913.03
            break
        try:
            custody_fee = float(user_input)
            break
        except ValueError:
            print("Invalid date format. Please enter a valid date.")

    print('----------------------------------------')

    # report_date = '2024-11-15'
    # cash = 1374364.73
    # diff_market_value = -202862.22
    # diff_mgt_fee = 175480.98
    # custody_fee = 93918.37

    if db_type == 'sqlite3':
        db = SQLITE3_DBHandler()
    else:
        db = MSSQL_DBHandler()

    # fund_name = 'client_account_a'

    offset = 3

    # 整合trade record
    print('Processing the bond transaction data of {}...'.format(fund_name))
    # bba_data_path = './bba_data/bba_data_trade_record.xlsx'
    bba_data_path = bba_data_trade_record_path
    bba_trade_record_df = pd.read_excel(bba_data_path, index_col=None, usecols=[0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30], parse_dates=True, header=0, skiprows=0)
    bba_trade_record_df['trade_ticket'] = bba_trade_record_df['trade_ticket'].astype(str)
    # 从数据库载入所有交易记录（trade record）
    sql = "select * from bond_trade_record where fund_name = '" + fund_name + "' and as_date <= '" + report_date + "' order by as_date"
    trade_record_df = db.query(sql)
    if trade_record_df.empty:
        print('The bond transaction data of {} not found in the database'.format(fund_name))
        input("Please press RETURN to exit...")
        sys.exit()
    trade_record_df = trade_record_df.drop(columns=['index_no', 'fund_name'])

    trade_record_df = pd.merge(trade_record_df, bba_trade_record_df[['trade_ticket', 'name_chinese_traditional']], on='trade_ticket', how='left')
    trade_record_df['pm'] = trade_record_df['isin_code'] + ' @BVAL Corp'
    trade_record_df = pd.merge(trade_record_df, bba_trade_record_df[['trade_ticket', 'security_des', 'cpn', 'maturity']], on='trade_ticket', how='left')
    # trade_record_df['daily_amortization_rate'] = (100 - trade_record_df['trade_price']) / count_between_date(trade_record_df['maturity'], trade_record_df['settle_date'])
    # trade_record_df['face_value'] = trade_record_df['trade_amount'] * 1000 / 100 * trade_record_df['trade_price'] + trade_record_df['daily_amortization_rate'] * max(0, count_between_date(report_date, trade_record_df['settle_date']) + 1)
    # trade_record_df = pd.merge(trade_record_df, bba_df[['trade_ticket', 'accrued_interest', 'ai_coupon_date']], on='trade_ticket', how='left')
    #
    # def calculate_ai_receivable(row):
    #     if count_between_date(report_date, row['settle_date']) >= 0:
    #         return 0
    #     else:
    #         if row['trade_direction'] == 'S':
    #             return -row['accrued_interest']
    #         elif row['trade_direction'] == 'B':
    #             return row['accrued_interest']
    # trade_record_df['ai_receivable_unsettle_trade'] = trade_record_df.apply(calculate_ai_receivable, axis=1)
    #
    # def calculate_maturity_or_not(row):
    #     if count_between_date(report_date, row['maturity']) >= 0:
    #         return "YES"
    #     else:
    #         return "NO"
    # trade_record_df['maturity_or_not'] = trade_record_df.apply(calculate_maturity_or_not, axis=1)
    #
    # def calculate_total_value(row):
    #     if row['maturity_or_not'] == 'YES':
    #         return 0
    #     elif row['maturity_or_not'] == 'NO':
    #         if row['trade_direction'] == 'B':
    #             if report_date >= row['settle_date']:
    #                 return row['face_value'] + row['accrued_interest'] + row['ai_receivable_unsettle_trade']
    #             else:
    #                 return row['face_value']
    #         elif row['trade_direction'] == 'S':
    #             if report_date >= row['settle_date']:
    #                 return -(row['face_value'] + row['accrued_interest'] + row['ai_receivable_unsettle_trade'])
    #             else:
    #                 return -row['face_value']
    # trade_record_df['total_value'] = trade_record_df.apply(calculate_total_value, axis=1)
    #
    # trade_record_df['maturity_chinese'] = trade_record_df['maturity'].dt.strftime('%Y年%m月%d日')
    #
    # def calculate_actual_holding(row):
    #     if row['trade_direction'] == 'B':
    #         return row['trade_amount']
    #     elif row['trade_direction'] == 'S':
    #         return -row['trade_amount']
    # trade_record_df['actual_holding'] = trade_record_df.apply(calculate_actual_holding, axis=1)
    #
    # sumif_dict = trade_record_df.groupby('isin_code')['actual_holding'].sum().to_dict()
    # def calculate_exist_or_not(row):
    #     sumif_result = sumif_dict.get(row['isin_code'], 0)
    #     return "NO" if sumif_result == 0 else "YES"
    # trade_record_df['exist_or_not'] = trade_record_df.apply(lambda row: calculate_exist_or_not(row, sumif_dict), axis=1)
    #
    # def calculate_total_value_no_exist_bonds(row):
    #     if row['exist_or_not'] == 'YES':
    #         return row['total_value']
    #     else:
    #         return 0
    # trade_record_df['total_value_no_exist_bonds'] = trade_record_df.apply(calculate_total_value_no_exist_bonds, axis=1)
    #
    #
    # def calculate_pct_nav(row):
    #     if row['maturity_or_not'] == 'YES' or row['exist_or_not'] == 'NO':
    #         return 0
    #     else:
    #         return row['total_value_no_exist_bonds'] / sum(trade_record_df['total_value_no_exist_bonds'])
    # trade_record_df['pct_nav'] = trade_record_df.apply(calculate_pct_nav, axis=1)
    #
    # trade_record_df['code'] = trade_record_df['sec_desc'].apply(lambda x: x[:x.find(" ")] if x.find(" ") != -1 else x)
    # trade_record_df['daily_amortization_rate'] = trade_record_df.apply(lambda row: f'=IF(AND(E{row.name + offset}="S", $S$1-$V{row.name + offset}>=0), 0, (100-J{row.name + offset})/(V{row.name + offset}-B{row.name + offset}))', axis=1)
    trade_record_df['daily_amortization_rate'] = trade_record_df.apply(lambda row: f'=IF(O{row.name + offset}="MTD", 0, (100-J{row.name + offset})/(V{row.name + offset}-B{row.name + offset}))', axis=1)
    trade_record_df['face_value'] = trade_record_df.apply(lambda row: f'=I{row.name + offset}*1000/100*(J{row.name + offset}+W{row.name + offset}*MAX(0,$S$1-B{row.name + offset}+1))', axis=1)
    trade_record_df = pd.merge(trade_record_df, bba_trade_record_df[['trade_ticket', 'accrued_interest', 'ai_coupon_date']], on='trade_ticket', how='left')
    trade_record_df['ai_receivable_unsettle_trade'] = trade_record_df.apply(lambda row: f'=IF($AL{row.name + offset}="NO",0,IF($E{row.name + offset}="S",-(IF($S$1-$B{row.name + offset}>=0,0,$Y{row.name + offset})),IF($S$1-$B{row.name + offset}>=0,0,$Y{row.name + offset})))', axis=1)
    trade_record_df['maturity_or_not'] = trade_record_df.apply(lambda row: f'=IF($S$1-$V{row.name + offset}>=0,"YES","NO")', axis=1)
    trade_record_df['total_value'] = trade_record_df.apply(lambda row: f'=IF(AB{row.name + offset}="YES",0,IF(E{row.name + offset}="B",IF($S$1>=$B{row.name + offset},$X{row.name + offset}+$Y{row.name + offset}+$Z{row.name + offset},$X{row.name + offset}),IF($S$1>=$B{row.name + offset},-($X{row.name + offset}+$Y{row.name + offset}+$Z{row.name + offset}),-$X{row.name + offset})))', axis=1)
    trade_record_df['maturity_chinese'] = trade_record_df.apply(lambda row: f'=TEXT(V{row.name + offset},"e/m/d")', axis=1)
    # trade_record_df['actual_holding'] = trade_record_df.apply(lambda row: f'=IF(E{row.name + offset}="B",I{row.name + offset},-I{row.name + offset})', axis=1)
    trade_record_df['actual_holding'] = trade_record_df.apply(lambda x: x['trade_amount'] if x['trade_direction'] == 'B' else -x['trade_amount'], axis=1)
    trade_record_df['pct_nav'] = trade_record_df.apply(lambda row: f'=IF(OR(AB{row.name + offset}="YES",AL{row.name + offset}="NO"),0,IF(ISBLANK($A{row.name + offset}),"",AM{row.name + offset}/(SUM(AM$3:AM$1048576)+' + str(cash) + '+SUM(trade_record_mbs!AD$3:AD$1048576)+repo_trade_record!AE1)))', axis=1)
    trade_record_df['code'] = trade_record_df.apply(lambda row: f'=LEFT(T{row.name + offset},(FIND(" ",T{row.name + offset},1)-1))', axis=1)
    trade_record_df['chinese_name'] = trade_record_df.apply(lambda row: f'=VLOOKUP(AG{row.name + offset},Classification!$B:$D,2,FALSE)', axis=1)
    # trade_record_df = pd.merge(trade_record_df, bba_trade_record_df[['trade_ticket', 'name_chinese_simplified']], on='trade_ticket', how='left')
    # trade_record_df.rename(columns={'name_chinese_simplified': 'chinese_name'}, inplace=True)
    trade_record_df['classification'] = trade_record_df.apply(lambda row: f'=VLOOKUP(AG{row.name + offset},Classification!$B:$D,3,FALSE)', axis=1)
    trade_record_df['side+isin'] = trade_record_df.apply(lambda row: f'=E{row.name + offset}&F{row.name + offset}', axis=1)
    trade_record_df['last_purchase_date'] = trade_record_df.apply(lambda row: f'=IF(E{row.name + offset}="B",TEXT(MAXIFS($A:$A,$F:$F,$F{row.name + offset}),"e/m/d"),VLOOKUP("B"&$F{row.name + offset},$AJ:$AK,2,0))', axis=1)
    # trade_record_df['exist_or_not'] = trade_record_df.apply(lambda row: f'=IF(SUMIF(F:F,F{row.name + offset},AE:AE)=0,"NO","YES")', axis=1)
    # trade_record_df['cumulative_holding'] = trade_record_df.groupby('isin_code')['actual_holding'].cumsum()
    # trade_record_df['exist_or_not'] = 'YES'
    # trade_record_df.loc[trade_record_df['cumulative_holding'] == 0, 'exist_or_not'] = 'NO'
    # trade_record_df = trade_record_df.drop(columns=['cumulative_holding'])

    def mark_zero_groups(group):
        group = group.copy()
        cumsum = group['actual_holding'].cumsum()
        group['exist_or_not'] = 'YES'
        zero_indices = []
        # 找出每次归零的位置
        zero_positions = cumsum[cumsum == 0].index.tolist()
        for pos in zero_positions:
            # 找到该次归零的所有起点（即上次归零之后到现在为止的范围）
            start_idx = group.index[0] if not zero_indices else zero_indices[-1] + 1
            end_idx = pos
            zero_indices.append(end_idx)
            # 将这段区间内的行标记为 'NO'
            group.loc[start_idx:end_idx, 'exist_or_not'] = 'NO'
        return group
    trade_record_df = trade_record_df.groupby('isin_code', group_keys=False).apply(mark_zero_groups)

    trade_record_df['total_value_no_exist_bonds'] = trade_record_df.apply(lambda row: f'=IF(AL{row.name + offset}="YES",AC{row.name + offset},0)', axis=1)
    # trade_record_df['px_mid'] = trade_record_df.apply(lambda row: f'=IF($S$1=TODAY(),BDP($S{row.name + offset},$AN$2),BDH($S{row.name + offset},$AN$2,$S$1))', axis=1)
    trade_record_df = pd.merge(trade_record_df, bba_trade_record_df[['trade_ticket', 'px_mid']], on='trade_ticket', how='left')
    trade_record_df['accrued_interest_2'] = trade_record_df.apply(lambda row: f'=IF(AA{row.name + offset}=0,IF(E{row.name + offset}="B",Y{row.name + offset},-Y{row.name + offset}),0)', axis=1)
    trade_record_df['total_value_mm'] = trade_record_df.apply(lambda row: f'=IFERROR(IF(E{row.name + offset}="B",AN{row.name + offset}*I{row.name + offset}*1000/100+AO{row.name + offset},-AN{row.name + offset}*I{row.name + offset}*1000/100+AO{row.name + offset}),0)', axis=1)
    trade_record_df['yield_2'] = trade_record_df.apply(lambda row: f'=Q{row.name + offset}', axis=1)
    trade_record_df['actual_holding_2'] = trade_record_df.apply(lambda row: f'=IF(E{row.name + offset}="B",I{row.name + offset},0)', axis=1)
    trade_record_df['yield*qty'] = trade_record_df.apply(lambda row: f'=AQ{row.name + offset}*AR{row.name + offset}', axis=1)
    trade_record_df['purchase_price*qty'] = trade_record_df.apply(lambda row: f'=J{row.name + offset}*AR{row.name + offset}', axis=1)
    trade_record_df = pd.merge(trade_record_df, bba_trade_record_df[['trade_ticket', 'int_acc']], on='trade_ticket', how='left')
    trade_record_df['clean_value'] = trade_record_df.apply(lambda row: f'=AM{row.name + offset}/(1+AU{row.name + offset}/100)', axis=1)
    trade_record_df['face_value_2'] = trade_record_df.apply(lambda row: f'=IF(E{row.name + offset}="B",I{row.name + offset}*1000,-I{row.name + offset}*1000)', axis=1)
    trade_record_df = pd.merge(trade_record_df, bba_trade_record_df[['trade_ticket', 'is_perpetual', 'nxt_call_dt', 'tlac_mrel_designation', 'basel_iii_designation', 'callable', 'issuer', 'cntry_issue_iso', 'guarantor_name', 'iso_country_guarantor', 'keep_well_name', 'keep_well_country_iso', 'standby_loc_name', 'standby_loc_country_iso', 'industry_group']], on='trade_ticket', how='left')

    bond_trade_record_df = trade_record_df.copy()
    trade_record_df = None

    # 整合repo trade record
    print('Processing the repo transaction data of {}...'.format(fund_name))
    # bba_data_path = './bba_data/bba_data_repo_trade_record.xlsx'
    bba_data_path = bba_data_repo_trade_record_path
    bba_repo_trade_record_df = pd.read_excel(bba_data_path, index_col=None, usecols=[0, 1, 2, 3, 4], parse_dates=True, header=0, skiprows=0)
    bba_repo_trade_record_df['trade_ticket'] = bba_repo_trade_record_df['trade_ticket'].astype(str)
    # 从数据库载入所有交易记录（repo trade record）
    sql = "select * from bond_repo_trade_record where fund_name = '" + fund_name + "' and as_date <= '" + report_date + "' order by as_date"
    repo_trade_record_df = db.query(sql)
    if repo_trade_record_df.empty:
        print('The repo transaction data of {} not found in the database'.format(fund_name))
        input("Please press RETURN to exit...")
        sys.exit()
    repo_trade_record_df = repo_trade_record_df.drop(columns=['index_no', 'fund_name'])
    repo_trade_record_df['is_alive'] = None
    # grouped_df = repo_trade_record_df.groupby(['as_date', 'isin_code', 'repo_rate', 'firm_broker'], group_keys=False)
    # # 遍历 groupby 对象以查看每个分组的数据
    # for group_name, group_data in grouped_df:
    #     print(f"Group: {group_name}")
    #     print(group_data)
    #     print("\n---\n")
    repo_trade_record_df = repo_trade_record_df.reset_index()
    repo_trade_record_df['repo_loan_amount_round'] = np.floor(repo_trade_record_df['repo_loan_amount'] + 0.5).astype(int)
    repo_trade_record_df = repo_trade_record_df.groupby(['as_date', 'isin_code', 'repo_loan_amount_round', 'firm_broker'], group_keys=False).apply(process_group)
    repo_trade_record_df.dropna(subset=['isin_code'], inplace=True)
    repo_trade_record_df = repo_trade_record_df.sort_values(by=['index'])
    repo_trade_record_df = repo_trade_record_df.drop(columns=['index'])

    repo_trade_record_df['security_des'] = repo_trade_record_df.apply(lambda row: f'=H{row.name + offset}' if row['is_alive'] == 'YES' else '', axis=1)
    repo_trade_record_df['repo_rate_2'] = repo_trade_record_df.apply(lambda row: f'=K{row.name + offset}' if row['is_alive'] == 'YES' else '', axis=1)
    repo_trade_record_df['maturity'] = repo_trade_record_df.apply(lambda row: f'=IF(V{row.name + offset}="Active",IF($U{row.name + offset}="Term",$C{row.name + offset},"OPEN"),IF(V{row.name + offset}="Closed",C{row.name + offset},C{row.name + offset}))' if row['is_alive'] == 'YES' else '', axis=1)
    repo_trade_record_df['daily_amortization_rate'] = repo_trade_record_df.apply(lambda row: f'=$K{row.name + offset}/100*$N{row.name + offset}/360' if row['is_alive'] == 'YES' else '', axis=1)
    repo_trade_record_df['face_value'] = repo_trade_record_df.apply(lambda row: f'=IF($V{row.name + offset}="Active",IF($U{row.name + offset}="Term",IF($C{row.name + offset}-$Y$1<=0,0,-$N{row.name + offset}),-$N{row.name + offset}),IF($V{row.name + offset}="Closed",IF($C{row.name + offset}-$Y$1>0,-$N{row.name + offset},0)))' if row['is_alive'] == 'YES' else '', axis=1)
    repo_trade_record_df['accrued_interest_2'] = repo_trade_record_df.apply(lambda row: f'=IF($B{row.name + offset}-$Y$1>0,0,IF($V{row.name + offset}="Active",IF($U{row.name + offset}="Term",IF($C{row.name + offset}-$Y$1<=0,0,-$AA{row.name + offset}*($Y$1-$B{row.name + offset}+1)),-$AA{row.name + offset}*($Y$1-$B{row.name + offset}+1)),IF($V{row.name + offset}="Closed",IF($C{row.name + offset}-$Y$1>0,-$AA{row.name + offset}*($Y$1-$B{row.name + offset}+1),0),IF($C{row.name + offset}-$Y$1>0,-$AA{row.name + offset}*($Y$1-$B{row.name + offset}+1),0))))' if row['is_alive'] == 'YES' else '', axis=1)
    repo_trade_record_df['maturity_terminated_or_not'] = repo_trade_record_df.apply(lambda row: f'=IF(U{row.name + offset}="Open","NO",IF($C{row.name + offset}-$Y$1>0,"NO","YES"))' if row['is_alive'] == 'YES' else '', axis=1)
    repo_trade_record_df['total_value'] = repo_trade_record_df.apply(lambda row: f'=$AB{row.name + offset}+$AC{row.name + offset}' if row['is_alive'] == 'YES' else '', axis=1)
    repo_trade_record_df = pd.merge(repo_trade_record_df, bba_repo_trade_record_df[['trade_ticket', 'next_coupon_settle_date_rt']], on='trade_ticket', how='left')
    repo_trade_record_df = repo_trade_record_df.drop(columns=['is_alive', 'repo_loan_amount_round'])

    # 整合trade record mbs
    print('Processing the MBS transaction data of {}...'.format(fund_name))
    # bba_data_path = './bba_data/bba_data_trade_record_mbs.xlsx'
    bba_data_path = bba_data_trade_record_mbs_path
    bba_trade_record_mbs_df = pd.read_excel(bba_data_path, index_col=None, usecols=[0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26], parse_dates=True, header=0, skiprows=0)
    bba_trade_record_mbs_df['trade_ticket'] = bba_trade_record_mbs_df['trade_ticket'].astype(str)
    # 从数据库载入所有交易记录（trade record mbs）
    sql = "select * from bond_trade_record_mbs where fund_name = '" + fund_name + "' and as_date <= '" + report_date + "' order by as_date"
    trade_record_mbs_df = db.query(sql)
    if trade_record_mbs_df.empty:
        print('The MBS transaction data of {} not found in the database'.format(fund_name))
        input("Please press RETURN to exit...")
        sys.exit()
    trade_record_mbs_df = trade_record_mbs_df.drop(columns=['index_no', 'fund_name'])

    trade_record_mbs_df = pd.merge(trade_record_mbs_df, bba_trade_record_mbs_df[['trade_ticket', 'name_chinese_traditional']], on='trade_ticket', how='left')
    trade_record_mbs_df['pm'] = trade_record_mbs_df['isin_code'] + ' @BVAL Corp'
    trade_record_mbs_df = pd.merge(trade_record_mbs_df, bba_trade_record_mbs_df[['trade_ticket', 'security_des', 'cpn', 'maturity', 'mtg_factor']], on='trade_ticket', how='left')
    trade_record_mbs_df['daily_amortization_rate'] = trade_record_mbs_df.apply(lambda row: f'=IF(AND(E{row.name + offset}="S", $S$1-$V{row.name + offset}>=0), 0, (100-J{row.name + offset})/(V{row.name + offset}-B{row.name + offset}))', axis=1)
    trade_record_mbs_df['face_value'] = trade_record_mbs_df.apply(lambda row: f'=$I{row.name + offset}*W{row.name + offset}*1000/100*($J{row.name + offset}+$X{row.name + offset}*MAX(0,$S$1-$B{row.name + offset}+1))', axis=1)
    trade_record_mbs_df = pd.merge(trade_record_mbs_df, bba_trade_record_mbs_df[['trade_ticket', 'accrued_interest', 'ai_coupon_date']], on='trade_ticket', how='left')
    trade_record_mbs_df['ai_receivable_unsettle_trade'] = trade_record_mbs_df.apply(lambda row: f'=IF($E{row.name + offset}="S",-(IF($S$1-$B{row.name + offset}>=0,0,$Z{row.name + offset})),IF($S$1-$B{row.name + offset}>=0,0,$Z{row.name + offset}))', axis=1)
    trade_record_mbs_df['maturity_or_not'] = trade_record_mbs_df.apply(lambda row: f'=IF($S$1-$V{row.name + offset}>=0,"YES","NO")', axis=1)
    trade_record_mbs_df['total_value'] = trade_record_mbs_df.apply(lambda row: f'=IF(AC{row.name + offset}="YES",0,IF(E{row.name + offset}="B",IF($S$1>=$B{row.name + offset},$Y{row.name + offset}+$Z{row.name + offset}+$AA{row.name + offset},$Y{row.name + offset}),IF($S$1>=$B{row.name + offset},-($Y{row.name + offset}+$Z{row.name + offset}+$AA{row.name + offset}),-$Y{row.name + offset})))', axis=1)
    trade_record_mbs_df['maturity_chinese'] = trade_record_mbs_df.apply(lambda row: f'=TEXT(V{row.name + offset},"e/m/d")', axis=1)
    trade_record_mbs_df['actual_holding'] = trade_record_mbs_df.apply(lambda row: f'=IF(E{row.name + offset}="B",I{row.name + offset},-I{row.name + offset})', axis=1)
    trade_record_mbs_df['pct_nav'] = trade_record_mbs_df.apply(lambda row: f'=IF(OR(AC{row.name + offset}="YES",AM{row.name + offset}="NO"),0,IF(ISBLANK($A{row.name + offset}),"",AN{row.name + offset}/(SUM(AN$3:AN$1048576)+' + str(cash) + '+SUM(trade_record!AM$3:AM$1048576)+repo_trade_record!AE1)))', axis=1)
    trade_record_mbs_df = pd.merge(trade_record_mbs_df, bba_trade_record_mbs_df[['trade_ticket', 'mtg_gen_ticker']], on='trade_ticket', how='left')
    trade_record_mbs_df['chinese_name'] = trade_record_mbs_df.apply(lambda row: f'=VLOOKUP(AH{row.name + offset},Classification!$B:$D,2,FALSE)', axis=1)
    trade_record_mbs_df['classification'] = trade_record_mbs_df.apply(lambda row: f'=VLOOKUP(AH{row.name + offset},Classification!$B:$D,3,FALSE)', axis=1)
    trade_record_mbs_df['side+isin'] = trade_record_mbs_df.apply(lambda row: f'=E{row.name + offset}&F{row.name + offset}', axis=1)
    trade_record_mbs_df['last_purchase_date'] = trade_record_mbs_df.apply(lambda row: f'=IF(E{row.name + offset}="B",TEXT(MAXIFS($A:$A,$F:$F,$F{row.name + offset}),"e/m/d"),VLOOKUP("B"&$F{row.name + offset},$AK:$AL,2,0))', axis=1)
    trade_record_mbs_df['exist_or_not'] = trade_record_mbs_df.apply(lambda row: f'=IF(SUMIF(F:F,F{row.name + offset},AF:AF)=0,"NO","YES")', axis=1)
    trade_record_mbs_df['total_value_no_exist_bonds'] = trade_record_mbs_df.apply(lambda row: f'=IF(AM{row.name + offset}="YES",AD{row.name + offset},0)', axis=1)
    trade_record_mbs_df = pd.merge(trade_record_mbs_df, bba_trade_record_mbs_df[['trade_ticket', 'px_mid']], on='trade_ticket', how='left')
    trade_record_mbs_df['accrued_interest_2'] = trade_record_mbs_df.apply(lambda row: f'=IF(AB{row.name + offset}=0,IF(E{row.name + offset}="B",Z{row.name + offset},-Z{row.name + offset}),0)', axis=1)
    trade_record_mbs_df['total_value_mm'] = trade_record_mbs_df.apply(lambda row: f'=IFERROR(IF(E{row.name + offset}="B",AO{row.name + offset}*W{row.name + offset}*I{row.name + offset}*1000/100+AP{row.name + offset},-AO{row.name + offset}*W{row.name + offset}*I{row.name + offset}*1000/100+AP{row.name + offset}),0)', axis=1)
    trade_record_mbs_df['yield_2'] = trade_record_mbs_df.apply(lambda row: f'=Q{row.name + offset}', axis=1)
    trade_record_mbs_df['actual_holding_2'] = trade_record_mbs_df.apply(lambda row: f'=IF(E{row.name + offset}="B",I{row.name + offset},0)', axis=1)
    trade_record_mbs_df['yield*qty'] = trade_record_mbs_df.apply(lambda row: f'=AR{row.name + offset}*AS{row.name + offset}', axis=1)
    trade_record_mbs_df['purchase_price*qty'] = trade_record_mbs_df.apply(lambda row: f'=J{row.name + offset}*AS{row.name + offset}', axis=1)
    trade_record_mbs_df = pd.merge(trade_record_mbs_df, bba_trade_record_mbs_df[['trade_ticket', 'int_acc']], on='trade_ticket', how='left')
    trade_record_mbs_df['clean_value_mtg_factor'] = trade_record_mbs_df.apply(lambda row: f'=IF(E{row.name + offset}="B",Y{row.name + offset}/W{row.name + offset},-Y{row.name + offset}/W{row.name + offset})', axis=1)
    trade_record_mbs_df['settle_total_in_settle_currency_2'] = trade_record_mbs_df.apply(lambda row: f'=IF(E{row.name + offset}="B",L{row.name + offset},-L{row.name + offset})', axis=1)
    trade_record_mbs_df['face_value_2'] = trade_record_mbs_df.apply(lambda row: f'=IF(E{row.name + offset}="B",I{row.name + offset}*1000,-I{row.name + offset}*1000)', axis=1)
    trade_record_mbs_df = pd.merge(trade_record_mbs_df, bba_trade_record_mbs_df[['trade_ticket', 'issuer', 'cntry_issue_iso', 'guarantor_name', 'iso_country_guarantor', 'keep_well_name', 'keep_well_country_iso', 'standby_loc_name', 'standby_loc_country_iso', 'industry_group']], on='trade_ticket', how='left')

    # 读取calssification.xlsx文件
    # classification_data_path = './classification_data/classification_data.xlsx'
    classification_df = pd.read_excel(classification_data_path, index_col=None, parse_dates=True, header=None, skiprows=0)

    with xlw.App(visible=True) as app:
        # 创建一个新的工作簿
        wb = app.books[0]

        # 创建classification工作表
        print('Creating the sheet of classification...')
        sheet1 = wb.sheets[0]
        sheet1.range('A1').value = classification_df.values.tolist()  # 写入列名 + 数据
        sheet1.name = 'classification'

        sheet1.autofit()

        sheet2 = wb.sheets.add(name='trade_record')
        sheet3 = wb.sheets.add(name='repo_trade_record')
        sheet4 = wb.sheets.add(name='trade_record_mbs')

        # 创建trade_record工作表
        print('Creating the sheet of trade_record...')
        # sheet2 = wb.sheets.add(name='trade_record')
        sheet2.range('R1').value = "DATE"
        sheet2.range('S1').value = report_date
        last_day_of_previous_month = get_last_day_of_previous_month(report_date)
        sheet2.range('P1').value = "上个月末日期"
        # sheet2.range('Q1').value = last_day_of_previous_month
        sheet2.range('Q1').value = last_mgt_fee_date
        bond_trade_record_df = bond_trade_record_df.fillna('')
        sheet2.range('A2').value = [bond_trade_record_df.columns.tolist()] + bond_trade_record_df.values.tolist()  # 写入列名 + 数据

        sheet2.autofit()

        # 创建repo_trade_record工作表
        print('Creating the sheet of repo_trade_record...')
        # sheet3 = wb.sheets.add(name='repo_trade_record')
        sheet3.range('X1').value = "DATE"
        sheet3.range('Y1').value = report_date
        sheet3.range('A2').value = [repo_trade_record_df.columns.tolist()] + repo_trade_record_df.values.tolist()
        sheet3.range('Z:Z').number_format = 'yyyy/mm/dd'
        sheet3.range('AB1').value = "=SUM(AB3:AB1048576)"
        sheet3.range('AC1').value = "=SUM(AC3:AC1048576)"
        sheet3.range('AE1').value = "=SUM(AE3:AE1048576)"

        sheet3.autofit()

        # 创建trade_record_mbs工作表
        print('Creating the sheet of trade_record_mbs...')
        # sheet4 = wb.sheets.add(name='trade_record_mbs')
        sheet4.range('R1').value = "DATE"
        sheet4.range('S1').value = report_date
        sheet4.range('A2').value = [trade_record_mbs_df.columns.tolist()] + trade_record_mbs_df.values.tolist()

        sheet4.autofit()

        # 计算公式
        wb.app.calculate()  # 计算所有公式

        # 从 Excel 中读取计算后的数据并转换为 DataFrame
        data1 = sheet2.range('A2').expand().value
        bond_trade_record_df_calculated = pd.DataFrame(data1[1:], columns=data1[0])

        data2 = sheet3.range('A2').expand().value
        repo_trade_record_df_calculated = pd.DataFrame(data2[1:], columns=data2[0])

        data3 = sheet4.range('A2').expand().value
        trade_record_mbs_df_calculated = pd.DataFrame(data3[1:], columns=data3[0])

        # 创建pivot table dataframe(数据透视)
        print('Creating the sheet of pivot_table...')
        pivot_table_df = bond_trade_record_df_calculated[bond_trade_record_df_calculated['pct_nav'] != 0].reset_index(drop=True).groupby(['chinese_name', 'security_des', 'maturity_chinese', 'cpn']).apply(
            lambda x: pd.Series({
                'sum_of_purchase_price_wa': x['purchase_price*qty'].sum() / x['actual_holding_2'].sum(),
                'sum_of_yield_wa': x['yield*qty'].sum() / x['actual_holding_2'].sum(),
                'sum_of_current_price_amorized': x['clean_value'].sum() / x['actual_holding'].sum() / 10,
                'sum_of_settle_total_in_settle_currency': x['settle_total'].sum(),
                'sum_of_pct_nav': x['pct_nav'].sum(),
                'sum_of_total_value': x['total_value'].sum(),
                'sum_of_total_value_mm': x['total_value_mm'].sum()
            }), include_groups=False
        ).reset_index()
        # pivot_table_df = pivot_table_df.reset_index()

        # 创建pivot_table工作表
        sheet = wb.sheets.add(name='pivot_table')
        sheet.range('A1').value = [pivot_table_df.columns.tolist()] + pivot_table_df.values.tolist()
        sheet.range('E' + str(len(pivot_table_df) + 2)).value = "=SUM(E2:E" + str(len(pivot_table_df) + 1) + ")"
        sheet.range('F' + str(len(pivot_table_df) + 2)).value = "=SUM(F2:F" + str(len(pivot_table_df) + 1) + ")"
        sheet.range('G' + str(len(pivot_table_df) + 2)).value = "=SUM(G2:G" + str(len(pivot_table_df) + 1) + ")"
        sheet.range('H' + str(len(pivot_table_df) + 2)).value = "=SUM(H2:H" + str(len(pivot_table_df) + 1) + ")"
        sheet.range('I' + str(len(pivot_table_df) + 2)).value = "=SUM(I2:I" + str(len(pivot_table_df) + 1) + ")"
        sheet.range('J' + str(len(pivot_table_df) + 2)).value = "=SUM(J2:J" + str(len(pivot_table_df) + 1) + ")"
        sheet.range('K' + str(len(pivot_table_df) + 2)).value = "=SUM(K2:K" + str(len(pivot_table_df) + 1) + ")"

        sheet.autofit()

        # 创建pivot table upload dataframe(数据透视)
        print('Creating the sheet of pivot_table_upload...')
        pivot_table_upload_df = bond_trade_record_df_calculated[bond_trade_record_df_calculated['pct_nav'] != 0].reset_index(drop=True).groupby(['isin_code', 'chinese_name', 'security_des', 'last_purchase_date', 'maturity_chinese', 'cpn']).apply(
            lambda x: pd.Series({
                'sum_of_settle_total_in_settle_currency': x['settle_total'].sum(),
                'sum_of_pct_nav': x['pct_nav'].sum(),
                'sum_of_total_value': x['total_value'].sum(),
                'Average of Price': x['trade_price'].mean(),
                'sum_of_pct_nav2': x['pct_nav'].sum(),
                'sum_of_face_value': x['face_value'].sum(),
                'sum_of_accrued_interest2': x['accrued_interest_2'].sum()
            }), include_groups=False
        ).reset_index()
        # pivot_table_upload_df = pivot_table_upload_df.reset_index()

        # 创建pivot_table_upload工作表
        sheet = wb.sheets.add(name='pivot_table_upload')
        sheet.range('A1').value = [pivot_table_upload_df.columns.tolist()] + pivot_table_upload_df.values.tolist()
        sheet.range('G' + str(len(pivot_table_upload_df) + 2)).value = "=SUM(G2:G" + str(len(pivot_table_upload_df) + 1) + ")"
        sheet.range('H' + str(len(pivot_table_upload_df) + 2)).value = "=SUM(H2:H" + str(len(pivot_table_upload_df) + 1) + ")"
        sheet.range('I' + str(len(pivot_table_upload_df) + 2)).value = "=SUM(I2:I" + str(len(pivot_table_upload_df) + 1) + ")"
        sheet.range('J' + str(len(pivot_table_upload_df) + 2)).value = "=SUM(J2:J" + str(len(pivot_table_upload_df) + 1) + ")"
        sheet.range('K' + str(len(pivot_table_upload_df) + 2)).value = "=SUM(K2:K" + str(len(pivot_table_upload_df) + 1) + ")"
        sheet.range('L' + str(len(pivot_table_upload_df) + 2)).value = "=SUM(L2:L" + str(len(pivot_table_upload_df) + 1) + ")"
        sheet.range('M' + str(len(pivot_table_upload_df) + 2)).value = "=SUM(M2:M" + str(len(pivot_table_upload_df) + 1) + ")"

        sheet.autofit()

        # 创建pivot table mbs dataframe(数据透视)
        print('Creating the sheet of pivot_table_mbs...')
        pivot_table_mbs_df = trade_record_mbs_df_calculated[trade_record_mbs_df_calculated['pct_nav'] != 0].reset_index(drop=True).groupby(['chinese_name', 'security_des', 'maturity_chinese', 'cpn']).apply(
            lambda x: pd.Series({
                'sum_of_purchase_price_wa': x['purchase_price*qty'].sum() / x['actual_holding_2'].sum(),
                'sum_of_yield_wa': x['yield*qty'].sum() / x['actual_holding_2'].sum(),
                'sum_of_current_price_amorized': x['clean_value_mtg_factor'].sum() / x['actual_holding'].sum() / 10,
                'sum_of_settle_total_in_settle_currency': x['settle_total_in_settle_currency_2'].sum(),
                'sum_of_pct_nav': x['pct_nav'].sum(),
                'sum_of_total_value': x['total_value'].sum(),
                'sum_of_total_value_mm': x['total_value_mm'].sum()
            }), include_groups=False
        ).reset_index()
        # pivot_table_mbs_df = pivot_table_mbs_df.reset_index()

        # 创建pivot_table_mbs工作表
        sheet = wb.sheets.add(name='pivot_table_mbs')
        sheet.range('A1').value = [pivot_table_mbs_df.columns.tolist()] + pivot_table_mbs_df.values.tolist()
        sheet.range('E' + str(len(pivot_table_mbs_df) + 2)).value = "=SUM(E2:E" + str(len(pivot_table_mbs_df) + 1) + ")"
        sheet.range('F' + str(len(pivot_table_mbs_df) + 2)).value = "=SUM(F2:F" + str(len(pivot_table_mbs_df) + 1) + ")"
        sheet.range('G' + str(len(pivot_table_mbs_df) + 2)).value = "=SUM(G2:G" + str(len(pivot_table_mbs_df) + 1) + ")"
        sheet.range('H' + str(len(pivot_table_mbs_df) + 2)).value = "=SUM(H2:H" + str(len(pivot_table_mbs_df) + 1) + ")"
        sheet.range('I' + str(len(pivot_table_mbs_df) + 2)).value = "=SUM(I2:I" + str(len(pivot_table_mbs_df) + 1) + ")"
        sheet.range('J' + str(len(pivot_table_mbs_df) + 2)).value = "=SUM(J2:J" + str(len(pivot_table_mbs_df) + 1) + ")"
        sheet.range('K' + str(len(pivot_table_mbs_df) + 2)).value = "=SUM(K2:K" + str(len(pivot_table_mbs_df) + 1) + ")"

        sheet.autofit()

        # 创建交易流水df
        print('Creating the sheet of transaction flow...')
        trade_flow_df = pd.DataFrame(columns=['Ticker_1'])
        offset = 2
        trade_flow_df['Ticker_1'] = bond_trade_record_df['isin_code']
        trade_flow_df['Chinese Simplified'] = trade_flow_df.apply(lambda row: f'=VLOOKUP($F{row.name + offset},trade_record!$F:$AI,29,0)', axis=1)
        trade_flow_df['买入日期'] = trade_flow_df.apply(lambda row: f'=IF($E{row.name + offset}="B",TEXT(trade_record!A{row.name + offset + 1},"e/m/d"),"")', axis=1)
        trade_flow_df['卖出日期'] = trade_flow_df.apply(lambda row: f'=IF($E{row.name + offset}="S",TEXT(trade_record!A{row.name + offset + 1},"e/m/d"),"")', axis=1)
        trade_flow_df['Coupon'] = trade_flow_df.apply(lambda row: f'=VLOOKUP($F{row.name + offset},trade_record!$F:$U,16,0)', axis=1)
        trade_flow_df['B/S'] = trade_flow_df.apply(lambda row: f'=trade_record!$E{row.name + offset + 1}', axis=1)
        trade_flow_df['Ticker'] = trade_flow_df.apply(lambda row: f'=trade_record!$F{row.name + offset + 1}', axis=1)
        trade_flow_df['Quantity'] = trade_flow_df.apply(lambda row: f'=(trade_record!$I{row.name + offset + 1})*1000', axis=1)
        trade_flow_df['买入价格'] = trade_flow_df.apply(lambda row: f'=IF($E{row.name + offset}="B",trade_record!$J{row.name + offset + 1},"")', axis=1)
        trade_flow_df['卖出价格'] = trade_flow_df.apply(lambda row: f'=IF($E{row.name + offset}="S",trade_record!$J{row.name + offset + 1},"")', axis=1)
        trade_flow_df['Capital P/L'] = ''
        trade_flow_df['持有期间利息'] = ''
        trade_flow_df['Total P/L'] = trade_flow_df.apply(lambda row: f'=J{row.name + offset}+K{row.name + offset}', axis=1)
        trade_flow_df['市值（摊余成本法）'] = trade_flow_df.apply(lambda row: f'=trade_record!$AC{row.name + offset + 1}', axis=1)

        trade_flow_df = trade_flow_df.drop(columns=['Ticker_1'])

        # 创建交易流水工作表
        sheet_trade_flow = wb.sheets.add(name='交易流水')
        sheet_trade_flow.range('A1').value = [trade_flow_df.columns.tolist()] + trade_flow_df.values.tolist()

        # 计算公式
        wb.app.calculate()  # 计算所有公式

        data = sheet_trade_flow.range('A1').expand().value
        trade_flow_df_calculated = pd.DataFrame(data[1:], columns=data[0])
        trade_flow_df_calculated = calculate_capital_pl(trade_flow_df_calculated)
        trade_flow_df['Capital P/L'] = trade_flow_df_calculated['Capital P/L'] / 100
        sheet_trade_flow.range('A1').value = [trade_flow_df.columns.tolist()] + trade_flow_df.values.tolist()

        sheet_trade_flow.used_range.font.name = "Calibri"
        sheet_trade_flow.used_range.font.size = 11
        sheet_trade_flow.range('A1:M1').font.bold = True
        sheet_trade_flow.range('A1:M1').api.Borders.LineStyle = 1  # 线条样式（1为实线）
        sheet_trade_flow.range('A1:M1').api.Borders.Weight = 2  # 线条粗细（2为中等粗细）
        # sheet_trade_flow.range('A:M').api.AutoFilter()
        sheet_trade_flow.range('D:D').number_format = '#,##0.00'
        sheet_trade_flow.range('G:G').number_format = '#,##0.00'
        sheet_trade_flow.range('H:H').number_format = '#,##0.0000'
        sheet_trade_flow.range('I:I').number_format = '#,##0.0000'
        sheet_trade_flow.range('J:J').number_format = '_(* #,##0.00_);_(* (#,##0.00);_(* "-"??_);_(@_)'
        sheet_trade_flow.range('K:K').number_format = '#,##0.00'
        sheet_trade_flow.range('L:L').number_format = '_(* #,##0.00_);_(* (#,##0.00);_(* "-"??_);_(@_)'
        sheet_trade_flow.range('M:M').number_format = '_(* #,##0.00_);_(* (#,##0.00);_(* "-"??_);_(@_)'
        sheet_trade_flow.range('K:K').color = (251, 226, 213)
        sheet_trade_flow.range('B:C').number_format = 'yyyy/m/d'
        for row in sheet_trade_flow.range('E1:E' + str(sheet_trade_flow.used_range.last_cell.row)).rows:
            if row[0].value == 'S':
                sheet_trade_flow.range('A' + str(row.row) + ':M' + str(row.row)).api.Font.Color = 0x0000FF  # 红色
        # sheet_trade_flow.activate()
        sheet_trade_flow.range("A2").select()
        sheet_trade_flow.api.Application.ActiveWindow.FreezePanes = True

        sheet_trade_flow.autofit()

        # 创建交易明细列表df
        print('Creating the sheet of transaction detail...')
        trade_detail_df = pd.DataFrame(columns=['ISIN_1'])
        offset = 2
        trade_detail_df['ISIN_1'] = bond_trade_record_df['isin_code']
        trade_detail_df['交易日期'] = trade_detail_df.apply(lambda row: f'=IF($C{row.name + offset}=trade_record!$F{row.name + offset + 1},TEXT(trade_record!$A{row.name + offset + 1},"e/m/d"),"")', axis=1)
        trade_detail_df['Ticker'] = trade_detail_df.apply(lambda row: f'=IF($C{row.name + offset}=trade_record!$F{row.name + offset + 1},trade_record!$G{row.name + offset + 1},"")', axis=1)
        trade_detail_df['ISIN'] = trade_detail_df.apply(lambda row: f'=trade_record!$F{row.name + offset + 1}', axis=1)
        trade_detail_df['票息（%）'] = trade_detail_df.apply(lambda row: f'=VLOOKUP($C{row.name + offset},trade_record!$F:$U,16,FALSE)', axis=1)
        trade_detail_df['债券到期日'] = trade_detail_df.apply(lambda row: f'=TEXT(VLOOKUP($C{row.name + offset},trade_record!$F:$AD,17,FALSE),"e/m/d")', axis=1)
        trade_detail_df['剩余期限（年）'] = trade_detail_df.apply(lambda row: f'=IF((trade_record!$V{row.name + offset + 1}-trade_record!$S$1)<=0,"-",(trade_record!$V{row.name + offset + 1}-trade_record!$S$1)/360)', axis=1)
        trade_detail_df['信用主体'] = trade_detail_df.apply(lambda row: f'=VLOOKUP($C{row.name + offset},trade_record!$F:$AH,29,FALSE)', axis=1)
        trade_detail_df['行业'] = trade_detail_df.apply(lambda row: f'=VLOOKUP($G{row.name + offset},Classification!$C:$E,3,FALSE)', axis=1)
        trade_detail_df['B/S'] = trade_detail_df.apply(lambda row: f'=trade_record!$E{row.name + offset + 1}', axis=1)
        trade_detail_df['买入日期'] = trade_detail_df.apply(lambda row: f'=IF($I{row.name + offset}="B",IF($C{row.name + offset}=trade_record!$F{row.name + offset + 1},TEXT(trade_record!$A{row.name + offset + 1},"e/m/d"),""),"-")', axis=1)
        trade_detail_df['买入清算日期'] = trade_detail_df.apply(lambda row: f'=IF($I{row.name + offset}="B",IF($C{row.name + offset}=trade_record!$F{row.name + offset + 1},TEXT(trade_record!$B{row.name + offset + 1},"e/m/d"),""),"-")', axis=1)
        trade_detail_df['卖出日期'] = trade_detail_df.apply(lambda row: f'=IF($I{row.name + offset}="S",IF($C{row.name + offset}=trade_record!$F{row.name + offset + 1},TEXT(trade_record!$A{row.name + offset + 1},"e/m/d"),""),"-")', axis=1)
        trade_detail_df['卖出清算日期'] = trade_detail_df.apply(lambda row: f'=IF($I{row.name + offset}="S",IF($C{row.name + offset}=trade_record!$F{row.name + offset + 1},TEXT(trade_record!$B{row.name + offset + 1},"e/m/d"),""),"-")', axis=1)
        trade_detail_df['Quantity（M）'] = trade_detail_df.apply(lambda row: f'=IF($C{row.name + offset}=trade_record!$F{row.name + offset + 1},trade_record!$I{row.name + offset + 1}/1000,"")', axis=1)
        trade_detail_df['买入价格'] = trade_detail_df.apply(lambda row: f'=IF($I{row.name + offset}="B",IF(ISBLANK($C{row.name + offset}),"",trade_record!$J{row.name + offset + 1}),"-")', axis=1)
        trade_detail_df['卖出价格'] = trade_detail_df.apply(lambda row: f'=IF($I{row.name + offset}="S",IF(ISBLANK($C{row.name + offset}),"",trade_record!$J{row.name + offset + 1}),"-")', axis=1)
        trade_detail_df['成交收益率（%）'] = trade_detail_df.apply(lambda row: f'=trade_record!$Q{row.name + offset + 1}', axis=1)
        trade_detail_df['Capital P/L（FIFO）'] = trade_detail_df.apply(lambda row: f'=IF(交易流水!$J{row.name + offset}="","",交易流水!$J{row.name + offset})', axis=1)
        trade_detail_df['持有期间利息'] = trade_detail_df.apply(lambda row: f'=IF(交易流水!$K{row.name + offset}="","",交易流水!$K{row.name + offset})', axis=1)
        trade_detail_df['Total P/L'] = trade_detail_df.apply(lambda row: f'=IF(交易流水!$L{row.name + offset}="","",交易流水!$L{row.name + offset})', axis=1)

        trade_detail_df = trade_detail_df.drop(columns=['ISIN_1'])

        # 创建交易明细列表工作表
        sheet_trade_detail = wb.sheets.add(name='交易明细列表')
        sheet_trade_detail.range('A1').value = [trade_detail_df.columns.tolist()] + trade_detail_df.values.tolist()

        sheet_trade_detail.used_range.font.name = "Calibri"
        sheet_trade_detail.range('A1:T1').font.name = "华文楷体"
        sheet_trade_detail.used_range.font.size = 11
        sheet_trade_detail.range('A1:T1').font.bold = True
        sheet_trade_detail.range('A1:T' + str(sheet_trade_detail.used_range.last_cell.row)).api.Borders.LineStyle = 1  # 线条样式（1为实线）
        sheet_trade_detail.range('A1:T' + str(sheet_trade_detail.used_range.last_cell.row)).api.Borders.Weight = 2  # 线条粗细（2为中等粗细）
        sheet_trade_detail.range('A1:T1').color = (192, 192, 192)  # 中等亮度灰色
        sheet_trade_detail.range('N:N').number_format = '#,##0'
        sheet_trade_detail.range('O:O').number_format = '#,##0.000000'
        sheet_trade_detail.range('P:P').number_format = '#,##0.000000'
        sheet_trade_detail.range('R:R').number_format = '#,##0.00'
        sheet_trade_detail.range('S:S').number_format = '#,##0.00'
        sheet_trade_detail.range('T:T').number_format = '#,##0.00'
        sheet_trade_detail.range('A:A').number_format = 'yyyy/m/d'
        sheet_trade_detail.range('E:E').number_format = 'yyyy/m/d'
        sheet_trade_detail.range('J:M').number_format = 'yyyy/m/d'
        for row in sheet_trade_detail.range('I1:I' + str(sheet_trade_detail.used_range.last_cell.row)).rows:
            if row[0].value == 'S':
                sheet_trade_detail.range('A' + str(row.row) + ':T' + str(row.row)).api.Font.Color = 0x0000FF  # 红色

        sheet_trade_detail.autofit()

        # 创建交易流水MBS df
        print('Creating the sheet of transaction flow (MBS)...')
        trade_flow_mbs_df = pd.DataFrame(columns=['Ticker_1'])
        offset = 2
        trade_flow_mbs_df['Ticker_1'] = trade_record_mbs_df['isin_code']
        trade_flow_mbs_df['Chinese Simplified'] = trade_flow_mbs_df.apply(lambda row: f'=VLOOKUP($F{row.name + offset},trade_record_mbs!$F:$AI,30,0)', axis=1)
        trade_flow_mbs_df['买入日期'] = trade_flow_mbs_df.apply(lambda row: f'=IF($E{row.name + offset}="B",TEXT(trade_record_mbs!A{row.name + offset + 1},"e/m/d"),"")', axis=1)
        trade_flow_mbs_df['卖出日期'] = trade_flow_mbs_df.apply(lambda row: f'=IF($E{row.name + offset}="S",TEXT(trade_record_mbs!A{row.name + offset + 1},"e/m/d"),"")', axis=1)
        trade_flow_mbs_df['Coupon'] = trade_flow_mbs_df.apply(lambda row: f'=VLOOKUP($F{row.name + offset},trade_record_mbs!$F:$U,16,0)', axis=1)
        trade_flow_mbs_df['B/S'] = trade_flow_mbs_df.apply(lambda row: f'=trade_record_mbs!$E{row.name + offset + 1}', axis=1)
        trade_flow_mbs_df['Ticker'] = trade_flow_mbs_df.apply(lambda row: f'=trade_record_mbs!$F{row.name + offset + 1}', axis=1)
        trade_flow_mbs_df['Quantity'] = trade_flow_mbs_df.apply(lambda row: f'=(trade_record_mbs!$I{row.name + offset + 1})*1000', axis=1)
        trade_flow_mbs_df['买入价格'] = trade_flow_mbs_df.apply(lambda row: f'=IF($E{row.name + offset}="B",trade_record_mbs!$J{row.name + offset + 1},"")', axis=1)
        trade_flow_mbs_df['卖出价格'] = trade_flow_mbs_df.apply(lambda row: f'=IF($E{row.name + offset}="S",trade_record_mbs!$J{row.name + offset + 1},"")', axis=1)
        trade_flow_mbs_df['Capital P/L'] = ''
        trade_flow_mbs_df['持有期间利息'] = ''
        trade_flow_mbs_df['Total P/L'] = trade_flow_mbs_df.apply(lambda row: f'=J{row.name + offset}+K{row.name + offset}', axis=1)
        trade_flow_mbs_df['市值（摊余成本法）'] = trade_flow_mbs_df.apply(lambda row: f'=trade_record_mbs!$AD{row.name + offset + 1}', axis=1)

        # trade_flow_mbs_df['Date'] = pd.to_datetime(trade_flow_mbs_df['Date'], format='%Y年%m月%d日').dt.strftime('%Y/%m/%d')
        # # trade_flow_mbs_df['Quantity'] = pd.to_numeric(trade_flow_mbs_df['Quantity'], errors='coerce')
        # trade_flow_mbs_df['Quantity'] = trade_flow_mbs_df['Quantity'].apply(lambda x: '{:,.2f}'.format(float(x)))
        # trade_flow_mbs_df['买入价格'] = trade_flow_mbs_df['买入价格'].apply(lambda x: '{:,.6f}'.format(float(x)))
        # trade_flow_mbs_df['卖出价格'] = trade_flow_mbs_df['卖出价格'].apply(lambda x: '{:,.6f}'.format(float(x)))
        # trade_flow_mbs_df['Capital P/L'] = trade_flow_mbs_df['Capital P/L'].apply(lambda x: '{:,.2f}'.format(float(x)))
        # trade_flow_mbs_df['持有期间利息'] = trade_flow_mbs_df['持有期间利息'].apply(lambda x: '{:,.2f}'.format(float(x)))
        # trade_flow_mbs_df['Total P/L'] = trade_flow_mbs_df['Total P/L'].apply(lambda x: '{:,.2f}'.format(float(x)))
        # trade_flow_mbs_df['市值（摊余成本法）'] = trade_flow_mbs_df['市值（摊余成本法）'].apply(lambda x: '{:,.2f}'.format(float(x)))

        trade_flow_mbs_df = trade_flow_mbs_df.drop(columns=['Ticker_1'])

        # 创建交易流水MBS工作表
        sheet_trade_flow_mbs = wb.sheets.add(name='交易流水_MBS')
        sheet_trade_flow_mbs.range('A1').value = [trade_flow_mbs_df.columns.tolist()] + trade_flow_mbs_df.values.tolist()

        # 计算公式
        wb.app.calculate()  # 计算所有公式

        data = sheet_trade_flow_mbs.range('A1').expand().value
        trade_flow_mbs_df_calculated = pd.DataFrame(data[1:], columns=data[0])
        trade_flow_mbs_df_calculated = calculate_capital_pl(trade_flow_mbs_df_calculated)
        trade_flow_mbs_df['Capital P/L'] = trade_flow_mbs_df_calculated['Capital P/L']
        sheet_trade_flow_mbs.range('A1').value = [trade_flow_mbs_df.columns.tolist()] + trade_flow_mbs_df.values.tolist()

        sheet_trade_flow_mbs.used_range.font.name = "Calibri"
        sheet_trade_flow_mbs.used_range.font.size = 11
        sheet_trade_flow_mbs.range('A1:M1').font.bold = True
        sheet_trade_flow_mbs.range('A1:M1').api.Borders.LineStyle = 1  # 线条样式（1为实线）
        sheet_trade_flow_mbs.range('A1:M1').api.Borders.Weight = 2  # 线条粗细（2为中等粗细）
        # sheet_trade_flow_mbs.range('A:M').api.AutoFilter()
        sheet_trade_flow_mbs.range('G:G').number_format = '#,##0.00'
        sheet_trade_flow_mbs.range('H:H').number_format = '#,##0.000000'
        sheet_trade_flow_mbs.range('I:I').number_format = '#,##0.000000'
        sheet_trade_flow_mbs.range('J:J').number_format = '_(* #,##0.00_);_(* (#,##0.00);_(* "-"??_);_(@_)'
        sheet_trade_flow_mbs.range('K:K').number_format = '#,##0.00'
        sheet_trade_flow_mbs.range('L:L').number_format = '_(* #,##0.00_);_(* (#,##0.00);_(* "-"??_);_(@_)'
        sheet_trade_flow_mbs.range('M:M').number_format = '_(* #,##0.00_);_(* (#,##0.00);_(* "-"??_);_(@_)'
        sheet_trade_flow_mbs.range('K:K').color = (251, 226, 213)
        sheet_trade_flow_mbs.range('B:C').number_format = 'yyyy/m/d'
        for row in sheet_trade_flow_mbs.range('E1:E' + str(sheet_trade_flow_mbs.used_range.last_cell.row)).rows:
            if row[0].value == 'S':
                sheet_trade_flow_mbs.range('A' + str(row.row) + ':M' + str(row.row)).api.Font.Color = 0x0000FF  # 红色
        # sheet_trade_flow_mbs.activate()
        sheet_trade_flow_mbs.range("A2").select()
        sheet_trade_flow_mbs.api.Application.ActiveWindow.FreezePanes = True

        sheet_trade_flow_mbs.autofit()

        # 创建交易明细列表MBS df
        print('Creating the sheet of transaction detail (MBS)...')
        trade_detail_mbs_df = pd.DataFrame(columns=['ISIN_1'])
        offset = 2
        trade_detail_mbs_df['ISIN_1'] = trade_record_mbs_df['isin_code']
        trade_detail_mbs_df['交易日期'] = trade_detail_mbs_df.apply(lambda row: f'=IF($C{row.name + offset}=trade_record_mbs!$F{row.name + offset + 1},TEXT(trade_record_mbs!$A{row.name + offset + 1},"e/m/d/"),"")', axis=1)
        trade_detail_mbs_df['Ticker'] = trade_detail_mbs_df.apply(lambda row: f'=IF($C{row.name + offset}=trade_record_mbs!$F{row.name + offset + 1},trade_record_mbs!$G{row.name + offset + 1},"")', axis=1)
        trade_detail_mbs_df['ISIN'] = trade_detail_mbs_df.apply(lambda row: f'=trade_record_mbs!$F{row.name + offset + 1}', axis=1)
        trade_detail_mbs_df['票息（%）'] = trade_detail_mbs_df.apply(lambda row: f'=VLOOKUP($C{row.name + offset},trade_record_mbs!$F:$U,16,FALSE)', axis=1)
        trade_detail_mbs_df['债券到期日'] = trade_detail_mbs_df.apply(lambda row: f'=TEXT(VLOOKUP($C{row.name + offset},trade_record_mbs!$F:$AD,17,FALSE),"e/m/d")', axis=1)
        trade_detail_mbs_df['剩余期限（年）'] = trade_detail_mbs_df.apply(lambda row: f'=IF((trade_record_mbs!$V{row.name + offset + 1}-trade_record_mbs!$S$1)<=0,"-",(trade_record_mbs!$V{row.name + offset + 1}-trade_record_mbs!$S$1)/360)', axis=1)
        trade_detail_mbs_df['信用主体'] = trade_detail_mbs_df.apply(lambda row: f'=VLOOKUP($C{row.name + offset},trade_record_mbs!$F:$AI,30,FALSE)', axis=1)
        trade_detail_mbs_df['行业'] = trade_detail_mbs_df.apply(lambda row: f'=VLOOKUP($G{row.name + offset},Classification!$C:$E,3,FALSE)', axis=1)
        trade_detail_mbs_df['B/S'] = trade_detail_mbs_df.apply(lambda row: f'=trade_record_mbs!$E{row.name + offset + 1}', axis=1)
        trade_detail_mbs_df['买入日期'] = trade_detail_mbs_df.apply(lambda row: f'=IF($I{row.name + offset}="B",IF($C{row.name + offset}=trade_record_mbs!$F{row.name + offset + 1},TEXT(trade_record_mbs!$A{row.name + offset + 1},"e/m/d"),""),"-")', axis=1)
        trade_detail_mbs_df['买入清算日期'] = trade_detail_mbs_df.apply(lambda row: f'=IF($I{row.name + offset}="B",IF($C{row.name + offset}=trade_record_mbs!$F{row.name + offset + 1},TEXT(trade_record_mbs!$B{row.name + offset + 1},"e/m/d"),""),"-")', axis=1)
        trade_detail_mbs_df['卖出日期'] = trade_detail_mbs_df.apply(lambda row: f'=IF($I{row.name + offset}="S",IF($C{row.name + offset}=trade_record_mbs!$F{row.name + offset + 1},TEXT(trade_record_mbs!$A{row.name + offset + 1},"e/m/d"),""),"-")', axis=1)
        trade_detail_mbs_df['卖出清算日期'] = trade_detail_mbs_df.apply(lambda row: f'=IF($I{row.name + offset}="S",IF($C{row.name + offset}=trade_record_mbs!$F{row.name + offset + 1},TEXT(trade_record_mbs!$B{row.name + offset + 1},"e/m/d"),""),"-")', axis=1)
        trade_detail_mbs_df['Quantity（M）'] = trade_detail_mbs_df.apply(lambda row: f'=IF($C{row.name + offset}=trade_record_mbs!$F{row.name + offset + 1},trade_record_mbs!$I{row.name + offset + 1}/1000,"")', axis=1)
        trade_detail_mbs_df['买入价格'] = trade_detail_mbs_df.apply(lambda row: f'=IF($I{row.name + offset}="B",IF(ISBLANK($C{row.name + offset}),"",trade_record_mbs!$J{row.name + offset + 1}),"-")', axis=1)
        trade_detail_mbs_df['卖出价格'] = trade_detail_mbs_df.apply(lambda row: f'=IF($I{row.name + offset}="S",IF(ISBLANK($C{row.name + offset}),"",trade_record_mbs!$J{row.name + offset + 1}),"-")', axis=1)
        trade_detail_mbs_df['成交收益率（%）'] = trade_detail_mbs_df.apply(lambda row: f'=trade_record_mbs!$Q{row.name + offset + 1}', axis=1)
        trade_detail_mbs_df['Capital P/L（FIFO）'] = trade_detail_mbs_df.apply(lambda row: f'=IF(交易流水_MBS!$J{row.name + offset}="","",交易流水_MBS!$J{row.name + offset})', axis=1)
        trade_detail_mbs_df['持有期间利息'] = trade_detail_mbs_df.apply(lambda row: f'=IF(交易流水_MBS!$K{row.name + offset}="","",交易流水_MBS!$K{row.name + offset})', axis=1)
        trade_detail_mbs_df['Total P/L'] = trade_detail_mbs_df.apply(lambda row: f'=IF(交易流水_MBS!$L{row.name + offset}="","",交易流水_MBS!$L{row.name + offset})', axis=1)

        trade_detail_mbs_df = trade_detail_mbs_df.drop(columns=['ISIN_1'])

        # 创建交易明细列表MBS工作表
        sheet_trade_detail_mbs = wb.sheets.add(name='交易明细列表_MBS')
        sheet_trade_detail_mbs.range('A1').value = [trade_detail_mbs_df.columns.tolist()] + trade_detail_mbs_df.values.tolist()

        sheet_trade_detail_mbs.used_range.font.name = "Calibri"
        # sheet_trade_detail_mbs.range('A1:Z1').font.name = "华文楷体"
        sheet_trade_detail_mbs.used_range.font.size = 11
        sheet_trade_detail_mbs.range('A1:T1').font.bold = True
        sheet_trade_detail_mbs.range('A1:T1').api.Borders.LineStyle = 1  # 线条样式（1为实线）
        sheet_trade_detail_mbs.range('A1:T1').api.Borders.Weight = 2  # 线条粗细（2为中等粗细）
        sheet_trade_detail_mbs.range('A1:T1').color = (192, 192, 192)  # 中等亮度灰色
        # sheet_trade_detail_mbs.range('A:T').api.AutoFilter()
        sheet_trade_detail_mbs.range('N:N').number_format = '#,##0'
        sheet_trade_detail_mbs.range('O:O').number_format = '#,##0.000000'
        sheet_trade_detail_mbs.range('P:P').number_format = '#,##0.000000'
        sheet_trade_detail_mbs.range('R:R').number_format = '#,##0.00'
        sheet_trade_detail_mbs.range('S:S').number_format = '#,##0.00'
        sheet_trade_detail_mbs.range('T:T').number_format = '#,##0.00'
        sheet_trade_detail_mbs.range('A:A').number_format = 'yyyy/m/d'
        sheet_trade_detail_mbs.range('E:E').number_format = 'yyyy/m/d'
        sheet_trade_detail_mbs.range('J:M').number_format = 'yyyy/m/d'

        sheet_trade_detail_mbs.autofit()

        # 创建holding_df(持仓报告)
        print('Creating the sheet of bond holding report...')
        offset = 9
        trade_record_holding_df = pivot_table_df.copy()
        trade_record_holding_df.insert(2, '买入日', '')
        # trade_record_holding_df['买入日'] = trade_record_holding_df.apply(lambda row: f'=TEXT(INDEX(trade_record!A:A, MAX(IF(trade_record!G:G=持仓报告!C{row.name + offset}, ROW(trade_record!G:G), 0))),"e/m/d")', axis=1)
        trade_record_holding_df['买入日'] = trade_record_holding_df.apply(lambda row: f'=TEXT(INDEX(trade_record!A:A, AGGREGATE(14, 6, ROW(trade_record!G:G)/(trade_record!G:G=持仓报告!C{row.name + offset}), 1)), "e/m/d")', axis=1)
        trade_record_holding_df['maturity_chinese'] = trade_record_holding_df.apply(lambda row: f'=TEXT(INDEX(trade_record!AD:AD, AGGREGATE(14, 6, ROW(trade_record!G:G)/(trade_record!G:G=持仓报告!C{row.name + offset}), 1)), "e/m/d")', axis=1)
        trade_record_holding_df['投资分类'] = trade_record_holding_df.apply(lambda row: f'=VLOOKUP($B{row.name + offset},trade_record!$AH:$AI,2,0)', axis=1)
        trade_record_holding_df['境内评级'] = 'AAA'
        trade_record_mbs_holding_df = pivot_table_mbs_df.copy()
        trade_record_mbs_holding_df.insert(2, '买入日', '')
        # trade_record_mbs_holding_df['买入日'] = trade_record_mbs_holding_df.apply(lambda row: f'=TEXT(INDEX(trade_record_mbs!A:A, MAX(IF(trade_record_mbs!G:G=持仓报告!C{row.name + offset + len(trade_record_holding_df)}, ROW(trade_record_mbs!G:G), 0))),"e/m/d")', axis=1)
        trade_record_mbs_holding_df['买入日'] = trade_record_mbs_holding_df.apply(lambda row: f'=TEXT(INDEX(trade_record_mbs!A:A, AGGREGATE(14, 6, ROW(trade_record_mbs!G:G)/(trade_record_mbs!G:G=持仓报告!C{row.name + offset + len(trade_record_holding_df)}), 1)), "e/m/d")', axis=1)
        trade_record_mbs_holding_df['maturity_chinese'] = trade_record_holding_df.apply(lambda row: f'=TEXT(INDEX(trade_record_mbs!AE:AE, AGGREGATE(14, 6, ROW(trade_record_mbs!G:G)/(trade_record_mbs!G:G=持仓报告!C{row.name + offset + len(trade_record_holding_df)}), 1)), "e/m/d")', axis=1)
        trade_record_mbs_holding_df['投资分类'] = trade_record_mbs_holding_df.apply(lambda row: f'=VLOOKUP($B{(offset - 1) + len(trade_record_holding_df) + 1},trade_record_mbs!$AI:$AJ,2,0)', axis=1)
        trade_record_mbs_holding_df['境内评级'] = 'AAA'
        holding_df = pd.concat([trade_record_holding_df, trade_record_mbs_holding_df]).reset_index(drop=True)
        # holding_df = holding_df.drop(columns=['sum_of_settle_total_in_settle_currency'])
        new_row_list = [['现金（存于民生银行香港）', '', '', '', '', '', '', '', '', '=L7/SUM($L$7:$L$' + str((offset - 2) + len(holding_df) + 1) + ')', cash, '', '', ''],
                        ['债券正回购', '', '', '', '', '', '', '', '', '=L8/SUM($L$7:$L$' + str((offset - 2) + len(holding_df) + 1) + ')', '=repo_trade_record!AE1', '', '', '']]
        new_row_df = pd.DataFrame(new_row_list, columns=holding_df.columns)
        holding_df = pd.concat([new_row_df, holding_df]).reset_index(drop=True)
        holding_df = holding_df.rename(columns={'chinese_name': '科目(或债券发行人)', 'security_des': '债券名称', 'maturity_chinese': '到期日', 'cpn': '票息（%）', 'sum_of_purchase_price_wa': '买入价格（美元）', 'sum_of_yield_wa': '买入收益率（%）', 'sum_of_current_price_amorized': '估值价格（美元）', 'sum_of_settle_total_in_settle_currency': '投资金额（美元）', 'sum_of_pct_nav': '投资占比（%）', 'sum_of_total_value': '市值（美元）', 'sum_of_total_value_mm': '市值（市价法）'})

        # 创建持仓报告工作表
        sheet_holding = wb.sheets.add(name='持仓报告')
        sheet_holding.range('B5').value = '每日持仓报告'
        sheet_holding.range('B6').value = [holding_df.columns.tolist()] + holding_df.values.tolist()
        sheet_holding.range('B2').value = '报告日期'
        # sheet_holding.range('B3').value = pd.to_datetime(report_date).strftime('%Y/%m/%d')
        sheet_holding.range('B3').value = '=TEXT("' + report_date + '", "e/m/d")'
        sheet_holding.range('B3').number_format = 'yyyy/m/d'
        sheet_holding.range('D:D').number_format = 'yyyy/m/d'
        sheet_holding.range('E:E').number_format = 'yyyy/m/d'
        sheet_holding.range('B' + str((offset - 3) + len(holding_df) + 1)).value = '合计'
        sheet_holding.range('L' + str((offset - 3) + len(holding_df) + 1)).value = '=SUM(L7:L' + str((offset - 3) + len(holding_df)) + ')+' + str(diff_market_value)
        sheet_holding.range('B' + str((offset - 3) + len(holding_df) + 2)).value = '应收利息合计（若有）'
        sheet_holding.range('L' + str((offset - 3) + len(holding_df) + 2)).value = '=SUM(trade_record!AA3:AA' + str(len(bond_trade_record_df) + 2) + ')+' + 'SUM(trade_record_mbs!AB3:AB' + str(len(trade_record_mbs_df) + 2) + ')'
        sheet_holding.range('B' + str((offset - 3) + len(holding_df) + 3)).value = '管理费（每日计提）'
        sheet_holding.range('L' + str((offset - 3) + len(holding_df) + 3)).value = '=0.1%*($L$' + str((offset - 3) + len(holding_df) + 1) + '+$L$' + str((offset - 3) + len(holding_df) + 2) + ')/365*($B$3-trade_record!$Q$1+1)+' + str(diff_mgt_fee)
        sheet_holding.range('B' + str((offset - 3) + len(holding_df) + 4)).value = '托管费（每日计提）'
        sheet_holding.range('L' + str((offset - 3) + len(holding_df) + 4)).value = custody_fee
        sheet_holding.range('B' + str((offset - 3) + len(holding_df) + 5)).value = '组合净值'
        sheet_holding.range('L' + str((offset - 3) + len(holding_df) + 5)).value = '=+L' + str((offset - 3) + len(holding_df) + 1) + '+L' + str((offset - 3) + len(holding_df) + 2) + '-L' + str((offset - 3) + len(holding_df) + 3) + '-L' + str((offset - 3) + len(holding_df) + 4)

        sheet_holding.used_range.font.name = "Calibri"
        sheet_holding.used_range.font.size = 11
        sheet_holding.range('B2').font.name = "华文楷体"
        sheet_holding.range('B2').font.bold = True
        sheet_holding.range('B5:O5').merge()
        sheet_holding.range('B5').font.name = "华文楷体"
        sheet_holding.range('B5').font.size = 12
        sheet_holding.range('B5').api.Font.Underline = xlw.constants.UnderlineStyle.xlUnderlineStyleSingle
        sheet_holding.range('B5').font.bold = True
        sheet_holding.range('B5').api.HorizontalAlignment = xlw.constants.HAlign.xlHAlignCenter
        sheet_holding.range('B6:O6').font.name = "华文楷体"
        sheet_holding.range('B6:O6').font.size = 11
        sheet_holding.range('B6:O6').font.bold = True
        sheet_holding.range('B2:B3').api.HorizontalAlignment = xlw.constants.HAlign.xlHAlignCenter
        sheet_holding.range('B6:O' + str((offset - 3) + len(holding_df))).api.HorizontalAlignment = xlw.constants.HAlign.xlHAlignCenter
        sheet_holding.range('B' + str((offset - 3) + len(holding_df) + 1) + ':B' + str((offset - 3) + len(holding_df) + 5)).api.HorizontalAlignment = xlw.constants.HAlign.xlHAlignCenter
        sheet_holding.range('B6:B' + str((offset - 3) + len(holding_df) + 5)).font.name = "华文楷体"  # 名称列用楷体
        sheet_holding.range('D6:D' + str((offset - 3) + len(holding_df) + 5)).font.name = "华文楷体"  # 名称列用楷体
        sheet_holding.range('E6:E' + str((offset - 3) + len(holding_df) + 5)).font.name = "华文楷体"  # 名称列用楷体
        sheet_holding.range('B2:B3').api.Borders.LineStyle = 1  # 线条样式（1为实线）
        sheet_holding.range('B2:B3').api.Borders.Weight = 2  # 线条粗细（2为中等粗细）
        sheet_holding.range('B6:O6').api.Borders.LineStyle = 1  # 线条样式（1为实线）
        sheet_holding.range('B6:O6').api.Borders.Weight = 2  # 线条粗细（2为中等粗细）
        sheet_holding.range('B6:O6').color = (192, 192, 192)  # 中等亮度灰色
        for row in sheet_holding.range('B7:O' + str((offset - 3) + len(holding_df))).rows:
            row.api.Borders(9).LineStyle = 1  # 线条样式（1为实线）
            row.api.Borders(9).Weight = 2  # 线条粗细（2为中等粗细）
        sheet_holding.range('L' + str((offset - 3) + len(holding_df) + 1) + ':L' + str((offset - 3) + len(holding_df) + 5)).api.Borders.LineStyle = 1  # 线条样式（1为实线）
        sheet_holding.range('L' + str((offset - 3) + len(holding_df) + 1) + ':L' + str((offset - 3) + len(holding_df) + 5)).api.Borders.Weight = 2  # 线条粗细（2为中等粗细）
        sheet_holding.range('F:M').number_format = '#,##0.00'
        sheet_holding.range('K:K').number_format = '0.00%'
        # app.screen_updating = False
        # app.display_alerts = False
        # app.calculation = 'manual'
        # sheet_holding.activate()
        app = xlw.apps.active
        app.api.ActiveWindow.DisplayGridlines = False
        # app.screen_updating = True
        # app.display_alerts = True
        # app.calculation = 'automatic'

        sheet_holding.autofit()

        # # 计算公式
        # wb.app.calculate()  # 计算所有公式
        #
        # # 从 Excel 中读取计算后的数据并转换为 DataFrame
        # data = sheet_trade_detail.range('A1').expand().value
        # trade_detail_df_calculated = pd.DataFrame(data[1:], columns=data[0])
        # data = sheet_trade_detail_mbs.range('A1').expand().value
        # trade_detail_mbs_df_calculated = pd.DataFrame(data[1:], columns=data[0])
        # trade_detail_df_merged = pd.concat([trade_detail_df_calculated, trade_detail_mbs_df_calculated])
        #
        # data = sheet_holding.range('B6:O' + str((offset - 3) + len(trade_record_holding_df))).expand().value
        # holding_df_calculated = pd.DataFrame(data[1:], columns=data[0])

        # 创建债券持仓明细df
        print('Creating the sheet of bond holding detail...')
        bond_holding_detail_df = pd.DataFrame(columns=['债券名称'])
        offset_holding = 9
        offset_holding_detail = 4
        bond_holding_detail_df['债券名称'] = holding_df['债券名称']
        bond_holding_detail_df = bond_holding_detail_df.iloc[2:].reset_index(drop=True)  # 因为最开始两行是'现金（存于民生......，所以要删除。
        bond_holding_detail_df['债券ISIN'] = bond_holding_detail_df.apply(lambda row: f'=IF(LEFT(B{row.name + offset_holding_detail},2)="G2",LEFT(VLOOKUP($B{row.name + offset_holding_detail},trade_record_mbs!$G:$S,13,FALSE),12),LEFT(VLOOKUP($B{row.name + offset_holding_detail},trade_record!$G:$S,13,FALSE),12))', axis=1)
        temp_trade_record_df = bond_trade_record_df[['isin_code', 'security_des']].drop_duplicates()
        temp_trade_record_mbs_df = trade_record_mbs_df[['isin_code', 'security_des']].drop_duplicates()
        temp_all_trade_record_df = pd.concat([temp_trade_record_df, temp_trade_record_mbs_df])
        bond_holding_detail_df['债券ISIN'] = bond_holding_detail_df['债券名称'].map(temp_all_trade_record_df.set_index('security_des')['isin_code'])
        bond_holding_detail_df['债券Ticker'] = bond_holding_detail_df.apply(lambda row: f'=持仓报告!C{row.name + offset_holding}', axis=1)
        bond_holding_detail_df['债券到期日'] = bond_holding_detail_df.apply(lambda row: f'=持仓报告!E{row.name + offset_holding}', axis=1)
        # bond_holding_detail_df['剩余期限(年)'] = bond_holding_detail_df.apply(lambda row: f'=IF(LEFT(B{row.name + offset_holding_detail},2)="G2",VLOOKUP(持仓报告!C{row.name + offset_holding},交易明细列表_MBS!$B:$F,5,FALSE),VLOOKUP(持仓报告!C{row.name + offset_holding},交易明细列表!$B:$F,5,FALSE))', axis=1)
        bond_holding_detail_df['剩余期限(年)'] = bond_holding_detail_df.apply(lambda row: f'=IF(LEFT(B{row.name + offset_holding_detail},2)="G2",VLOOKUP(持仓报告!C{row.name + offset_holding},交易明细列表_mbs!$B:$F,5,FALSE),VLOOKUP(持仓报告!C{row.name + offset_holding},交易明细列表!$B:$F,5,FALSE))', axis=1)
        bond_holding_detail_df['债券票息(%)'] = bond_holding_detail_df.apply(lambda row: f'=持仓报告!F{row.name + offset_holding}', axis=1)
        bond_holding_detail_df['信用主体名称'] = bond_holding_detail_df.apply(lambda row: f'=持仓报告!B{row.name + offset_holding}', axis=1)
        bond_holding_detail_df['票面金额'] = bond_holding_detail_df.apply(lambda row: f'=IF(LEFT(B{row.name + offset_holding_detail},2)="G2",SUMIF(trade_record_mbs!$G:$AY,持仓报告!$C{row.name + offset_holding},trade_record_mbs!$AY:$AY),SUMIF(trade_record!$G:$AW,持仓报告!$C{row.name + offset_holding},trade_record!$AW:$AW))', axis=1)
        bond_holding_detail_df['所属行业'] = bond_holding_detail_df.apply(lambda row: f'=VLOOKUP($F{row.name + offset_holding_detail},Classification!$C:$E,3,FALSE)', axis=1)
        bond_holding_detail_df['加权买入价'] = bond_holding_detail_df.apply(lambda row: f'=持仓报告!G{row.name + offset_holding}', axis=1)
        bond_holding_detail_df['加权收益率(%) - 持有成本'] = bond_holding_detail_df.apply(lambda row: f'=持仓报告!H{row.name + offset_holding}', axis=1)
        bond_holding_detail_df['现价'] = bond_holding_detail_df.apply(lambda row: f'=IF(LEFT(B{row.name + offset_holding_detail},2)="G2",VLOOKUP($A{row.name + offset_holding_detail},trade_record_mbs!$F$3:$AW$1048576,36,FALSE),VLOOKUP($A{row.name + offset_holding_detail},trade_record!$F$3:$AW$1048576,35,FALSE))', axis=1)
        # bond_holding_detail_df['加权收益率(%) - 市价估值'] = bond_holding_detail_df.apply(lambda row: f'=IF(LEFT(B4,2)="G2",BDP(A4&" MTGE","YLD_CNV_MID"),BDP(A4&" CORP","YAS_BOND_YLD","YAS_YLD_FLAG=15","YAS_BOND_PX",$K4))', axis=1)
        bba_temp_df = pd.concat([bba_trade_record_df[['isin_code', '加权收益率(%) - 市价估值', '久期', '涉险国家']].drop_duplicates(), bba_trade_record_mbs_df[['isin_code', '加权收益率(%) - 市价估值', '久期', '涉险国家']].drop_duplicates()]).reset_index(drop=True)
        bba_temp_df = bba_temp_df.rename(columns={'isin_code': '债券ISIN'})
        bond_holding_detail_df = pd.merge(bond_holding_detail_df, bba_temp_df[['债券ISIN', '加权收益率(%) - 市价估值']], on='债券ISIN', how='left')
        bond_holding_detail_df['市价损益'] = bond_holding_detail_df.apply(lambda row: f'=(K{row.name + offset_holding_detail}-I{row.name + offset_holding_detail})*G{row.name + offset_holding_detail}/100', axis=1)
        bond_holding_detail_df = pd.merge(bond_holding_detail_df, bba_temp_df[['债券ISIN', '久期', '涉险国家']], on='债券ISIN', how='left')
        # bond_holding_detail_df['涉险国家_CN'] = bond_holding_detail_df.apply(lambda row: f'=VLOOKUP(O{row.name + offset_holding_detail},Classification!$K:$L,2,FALSE)', axis=1)
        temp_classification_df = classification_df.iloc[:, [10, 11]]
        temp_classification_df.columns = ['country_en', 'country_cn']
        temp_classification_df = temp_classification_df.dropna()
        # bond_holding_detail_df['涉险国家_CN'] = bond_holding_detail_df['涉险国家'].map(temp_classification_df.set_index('country_en')['country_cn'])
        # bond_holding_detail_df.drop(columns=['涉险国家'], inplace=True)
        # bond_holding_detail_df.rename(columns={'涉险国家_CN': '涉险国家'}, inplace=True)
        bond_holding_detail_df['市值占比(%)'] = bond_holding_detail_df.apply(lambda row: f'=持仓报告!K{row.name + offset_holding}', axis=1)
        bond_holding_detail_df['投资分类'] = bond_holding_detail_df.apply(lambda row: f'=持仓报告!N{row.name + offset_holding}', axis=1)

        bond_holding_detail_df['债券发行人名称'] = bond_holding_detail_df.apply(lambda row: f'=IF(IF(LEFT(B{row.name + offset_holding_detail},2)="G2",VLOOKUP($A{row.name + offset_holding_detail},trade_record_mbs!$F$3:$ZZ$1048576,47,FALSE),VLOOKUP($A{row.name + offset_holding_detail},trade_record!$F$3:$ZZ$1048576,50,FALSE))="","",IF(LEFT(B{row.name + offset_holding_detail},2)="G2",VLOOKUP($A{row.name + offset_holding_detail},trade_record_mbs!$F$3:$ZZ$1048576,47,FALSE),VLOOKUP($A{row.name + offset_holding_detail},trade_record!$F$3:$ZZ$1048576,50,FALSE)))', axis=1)
        bond_holding_detail_df['发行人注册地国家'] = bond_holding_detail_df.apply(lambda row: f'=IF(IF(LEFT(B{row.name + offset_holding_detail},2)="G2",VLOOKUP($A{row.name + offset_holding_detail},trade_record_mbs!$F$3:$ZZ$1048576,48,FALSE),VLOOKUP($A{row.name + offset_holding_detail},trade_record!$F$3:$ZZ$1048576,51,FALSE))="","",IF(LEFT(B{row.name + offset_holding_detail},2)="G2",VLOOKUP($A{row.name + offset_holding_detail},trade_record_mbs!$F$3:$ZZ$1048576,48,FALSE),VLOOKUP($A{row.name + offset_holding_detail},trade_record!$F$3:$ZZ$1048576,51,FALSE)))', axis=1)
        bond_holding_detail_df['担保人名称'] = bond_holding_detail_df.apply(lambda row: f'=IF(IF(LEFT(B{row.name + offset_holding_detail},2)="G2",VLOOKUP($A{row.name + offset_holding_detail},trade_record_mbs!$F$3:$ZZ$1048576,49,FALSE),VLOOKUP($A{row.name + offset_holding_detail},trade_record!$F$3:$ZZ$1048576,52,FALSE))="","",IF(LEFT(B{row.name + offset_holding_detail},2)="G2",VLOOKUP($A{row.name + offset_holding_detail},trade_record_mbs!$F$3:$ZZ$1048576,49,FALSE),VLOOKUP($A{row.name + offset_holding_detail},trade_record!$F$3:$ZZ$1048576,52,FALSE)))', axis=1)
        bond_holding_detail_df['担保人注册地国家'] = bond_holding_detail_df.apply(lambda row: f'=IF(IF(LEFT(B{row.name + offset_holding_detail},2)="G2",VLOOKUP($A{row.name + offset_holding_detail},trade_record_mbs!$F$3:$ZZ$1048576,50,FALSE),VLOOKUP($A{row.name + offset_holding_detail},trade_record!$F$3:$ZZ$1048576,53,FALSE))="","",IF(LEFT(B{row.name + offset_holding_detail},2)="G2",VLOOKUP($A{row.name + offset_holding_detail},trade_record_mbs!$F$3:$ZZ$1048576,50,FALSE),VLOOKUP($A{row.name + offset_holding_detail},trade_record!$F$3:$ZZ$1048576,53,FALSE)))', axis=1)
        bond_holding_detail_df['维好人名称'] = bond_holding_detail_df.apply(lambda row: f'=IF(IF(LEFT(B{row.name + offset_holding_detail},2)="G2",VLOOKUP($A{row.name + offset_holding_detail},trade_record_mbs!$F$3:$ZZ$1048576,51,FALSE),VLOOKUP($A{row.name + offset_holding_detail},trade_record!$F$3:$ZZ$1048576,54,FALSE))="","",IF(LEFT(B{row.name + offset_holding_detail},2)="G2",VLOOKUP($A{row.name + offset_holding_detail},trade_record_mbs!$F$3:$ZZ$1048576,51,FALSE),VLOOKUP($A{row.name + offset_holding_detail},trade_record!$F$3:$ZZ$1048576,54,FALSE)))', axis=1)
        bond_holding_detail_df['维好人注册地国家'] = bond_holding_detail_df.apply(lambda row: f'=IF(IF(LEFT(B{row.name + offset_holding_detail},2)="G2",VLOOKUP($A{row.name + offset_holding_detail},trade_record_mbs!$F$3:$ZZ$1048576,52,FALSE),VLOOKUP($A{row.name + offset_holding_detail},trade_record!$F$3:$ZZ$1048576,55,FALSE))="","",IF(LEFT(B{row.name + offset_holding_detail},2)="G2",VLOOKUP($A{row.name + offset_holding_detail},trade_record_mbs!$F$3:$ZZ$1048576,52,FALSE),VLOOKUP($A{row.name + offset_holding_detail},trade_record!$F$3:$ZZ$1048576,55,FALSE)))', axis=1)
        bond_holding_detail_df['备证行名称'] = bond_holding_detail_df.apply(lambda row: f'=IF(IF(LEFT(B{row.name + offset_holding_detail},2)="G2",VLOOKUP($A{row.name + offset_holding_detail},trade_record_mbs!$F$3:$ZZ$1048576,53,FALSE),VLOOKUP($A{row.name + offset_holding_detail},trade_record!$F$3:$ZZ$1048576,56,FALSE))="","",IF(LEFT(B{row.name + offset_holding_detail},2)="G2",VLOOKUP($A{row.name + offset_holding_detail},trade_record_mbs!$F$3:$ZZ$1048576,53,FALSE),VLOOKUP($A{row.name + offset_holding_detail},trade_record!$F$3:$ZZ$1048576,56,FALSE)))', axis=1)
        bond_holding_detail_df['备证行注册地国家'] = bond_holding_detail_df.apply(lambda row: f'=IF(IF(LEFT(B{row.name + offset_holding_detail},2)="G2",VLOOKUP($A{row.name + offset_holding_detail},trade_record_mbs!$F$3:$ZZ$1048576,54,FALSE),VLOOKUP($A{row.name + offset_holding_detail},trade_record!$F$3:$ZZ$1048576,57,FALSE))="","",IF(LEFT(B{row.name + offset_holding_detail},2)="G2",VLOOKUP($A{row.name + offset_holding_detail},trade_record_mbs!$F$3:$ZZ$1048576,54,FALSE),VLOOKUP($A{row.name + offset_holding_detail},trade_record!$F$3:$ZZ$1048576,57,FALSE)))', axis=1)
        bond_holding_detail_df['行业分类'] = bond_holding_detail_df.apply(lambda row: f'=IF(IF(LEFT(B{row.name + offset_holding_detail},2)="G2",VLOOKUP($A{row.name + offset_holding_detail},trade_record_mbs!$F$3:$ZZ$1048576,55,FALSE),VLOOKUP($A{row.name + offset_holding_detail},trade_record!$F$3:$ZZ$1048576,58,FALSE))="","",IF(LEFT(B{row.name + offset_holding_detail},2)="G2",VLOOKUP($A{row.name + offset_holding_detail},trade_record_mbs!$F$3:$ZZ$1048576,55,FALSE),VLOOKUP($A{row.name + offset_holding_detail},trade_record!$F$3:$ZZ$1048576,58,FALSE)))', axis=1)

        bond_holding_detail_df = bond_holding_detail_df.drop(columns=['债券名称'])

        new_row_list = [['现金（存于民生银行香港）', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '=持仓报告!K' + str(offset_holding - 2), '', '', '', '', '', '', '', '', '', ''],
                        ['债券正回购', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '=持仓报告!K' + str(offset_holding - 1), '', '', '', '', '', '', '', '', '', '']]
        new_row_df = pd.DataFrame(new_row_list, columns=bond_holding_detail_df.columns)
        bond_holding_detail_df = pd.concat([new_row_df, bond_holding_detail_df]).reset_index(drop=True)

        # 创建持仓报告to债券持仓明细工作表
        sheet_bond_holding_detail_formula = wb.sheets.add(name='持仓报告to债券持仓明细')
        sheet_bond_holding_detail_formula.range('A1').value = [bond_holding_detail_df.columns.tolist()] + bond_holding_detail_df.values.tolist()

        sheet_bond_holding_detail_formula.autofit()

        # 计算公式
        wb.app.calculate()  # 计算所有公式

        data = sheet_bond_holding_detail_formula.range('A1').expand().value
        bond_holding_detail_df_calculated = pd.DataFrame(data[1:], columns=data[0])

        # 创建债券持仓明细工作表
        sheet_bond_holding_detail = wb.sheets.add(name='债券持仓明细')
        sheet_bond_holding_detail.range('B6').value = [bond_holding_detail_df_calculated.columns.tolist()] + bond_holding_detail_df_calculated.values.tolist()
        sheet_bond_holding_detail.range('B5').value = '每日持仓报告'
        sheet_bond_holding_detail.range('B2').value = '报告日期'
        sheet_bond_holding_detail.range('B3').value = pd.to_datetime(report_date).strftime('%Y年%m月%d日')

        sheet_bond_holding_detail.used_range.font.name = "Calibri"
        sheet_bond_holding_detail.used_range.font.size = 11
        sheet_bond_holding_detail.range('B2').font.name = "华文楷体"
        sheet_bond_holding_detail.range('B2').font.bold = True
        sheet_bond_holding_detail.range('B5:AA5').merge()
        sheet_bond_holding_detail.range('B5').font.name = "华文楷体"
        sheet_bond_holding_detail.range('B5').font.size = 12
        sheet_bond_holding_detail.range('B5').api.Font.Underline = xlw.constants.UnderlineStyle.xlUnderlineStyleSingle
        sheet_bond_holding_detail.range('B5').font.bold = True
        sheet_bond_holding_detail.range('B5').api.HorizontalAlignment = xlw.constants.HAlign.xlHAlignCenter
        sheet_bond_holding_detail.range('B6:AA6').font.name = "微软雅黑"
        sheet_bond_holding_detail.range('B6:AA6').font.size = 11
        sheet_bond_holding_detail.range('B6:AA6').font.bold = True
        sheet_bond_holding_detail.range('B2:B3').api.HorizontalAlignment = xlw.constants.HAlign.xlHAlignCenter
        sheet_bond_holding_detail.range('B6:AA' + str(len(bond_holding_detail_df) + 8)).api.HorizontalAlignment = xlw.constants.HAlign.xlHAlignCenter
        sheet_bond_holding_detail.range('H:H').font.name = "微软雅黑"  # 名称列用楷体
        sheet_bond_holding_detail.range('I:I').font.name = "微软雅黑"
        sheet_bond_holding_detail.range('P:P').font.name = "微软雅黑"
        sheet_bond_holding_detail.range('B7:B8').font.name = "微软雅黑"
        # sheet_bond_holding_detail.range('B6:B' + str((offset - 3) + len(holding_df))).font.name = "Calibri"  # MBS交易不使用华文楷体
        sheet_bond_holding_detail.range('B2:B3').api.Borders.LineStyle = 1  # 线条样式（1为实线）
        sheet_bond_holding_detail.range('B2:B3').api.Borders.Weight = 2  # 线条粗细（2为中等粗细）
        sheet_bond_holding_detail.range('B6:AA' + str(len(bond_holding_detail_df) + 6)).api.Borders.LineStyle = 1  # 线条样式（1为实线）
        sheet_bond_holding_detail.range('B6:AA' + str(len(bond_holding_detail_df) + 6)).api.Borders.Weight = 2  # 线条粗细（2为中等粗细）
        sheet_bond_holding_detail.range('B6:AA6').color = (192, 192, 192)  # 中等亮度灰色
        sheet_bond_holding_detail.range('E:O').number_format = '#,##0.00'
        sheet_bond_holding_detail.range('Q:Q').number_format = '0.00%'
        sheet_bond_holding_detail.range('D:D').number_format = 'yyyy/m/d'

        sheet_bond_holding_detail.autofit()

        # 创建账户整体概况list
        print('Creating the sheet of account status...')
        cash_movement_df = pd.read_excel(cash_movement_path, index_col=None, usecols=[0, 1], parse_dates=True, header=None, skiprows=0)
        cash_movement_df.columns = ['movement_name', 'movement_amount']
        df_length = len(cash_movement_df)

        overview_list = []
        overview_list.append(['报告日期', ''])
        # overview_list.append([pd.to_datetime(report_date).strftime('%Y/%m/%d'), ''])
        overview_list.append(['=TEXT("' + report_date + '", "e/m/d")', ''])
        overview_list.append(['', ''])
        overview_list.append(['账户总值-成本法', '=B13+B11+B10-B8+B5'])
        overview_list.append(['Repo（若有）', '=-持仓报告!L8'])
        overview_list.append(['借款（若有）', 0])
        overview_list.append(['证券清算款', 0])
        overview_list.append(['应计利息合计（若有）', '=VLOOKUP("应收利息合计（若有）",持仓报告!$B$7:$L$81,11,FALSE)'])
        overview_list.append(['其他应收款', 0])
        overview_list.append(['管理费（每日计提）', '=VLOOKUP($A$10,持仓报告!$B$7:$L$81,11,FALSE)'])
        overview_list.append(['托管费（每日计提）', '=VLOOKUP($A$11,持仓报告!$B$7:$L$81,11,FALSE)'])
        overview_list.append(['单位净值', '=B13/SUM(B14:B' + str(df_length + 13) + ')'])
        overview_list.append(['组合净值-成本法', '=VLOOKUP("组合净值",持仓报告!$B$7:$L$81,11,FALSE)'])

        overview_list.extend(cash_movement_df.values.tolist())
        overview_list.append(['', ''])
        overview_list.append(['', ''])
        overview_list.append(['', ''])
        overview_list.append(['', ''])
        overview_list.append(['账户总值-市值法', '=SUM(持仓报告!$M:$M)+持仓报告!L7'])
        overview_list.append(['Repo（若有）', '=B5'])
        overview_list.append(['借款（若有）', 0])
        overview_list.append(['证券清算款', 0])
        overview_list.append(['应计利息合计（若有）', '=B8'])
        overview_list.append(['其他应收款', 0])
        overview_list.append(['管理费（每日计提）', '=B10'])
        overview_list.append(['托管费（每日计提）', '=B11'])
        overview_list.append(['组合净值-市值法',
                              '=B' + str(df_length + 17 + 1) +
                              '-B' + str(df_length + 18 + 1) +
                              '+B' + str(df_length + 21 + 1) +
                              '-B' + str(df_length + 23 + 1) +
                              '-B' + str(df_length + 24 + 1)])

        # 创建账户整体概况工作表
        sheet_overview = wb.sheets.add(name='账户整体概况')
        sheet_overview.range('A1').value = overview_list

        sheet_overview.range('A1:A2').api.Borders.LineStyle = 1  # 线条样式（1为实线）
        sheet_overview.range('A1:A2').api.Borders.Weight = 2  # 线条粗细（2为中等粗细）
        sheet_overview.range('A4:B' + str(df_length + 12 + 1)).api.Borders.LineStyle = 1  # 线条样式（1为实线）
        sheet_overview.range('A4:B' + str(df_length + 12 + 1)).api.Borders.Weight = 2  # 线条粗细（2为中等粗细）
        sheet_overview.range('A' + str(df_length + 17 + 1) + ':B' + str(df_length + 25 + 1)).api.Borders.LineStyle = 1  # 线条样式（1为实线）
        sheet_overview.range('A' + str(df_length + 17 + 1) + ':B' + str(df_length + 25 + 1)).api.Borders.Weight = 2  # 线条粗细（2为中等粗细）
        sheet_overview.used_range.font.name = "Calibri"
        sheet_overview.range('A1').font.name = "华文楷体"
        sheet_overview.range('A4:A' + str(df_length + 25 + 1)).font.name = "华文楷体"
        sheet_overview.range('B4:B' + str(df_length + 25 + 1)).font.name = "华文楷体"
        sheet_overview.range('A:B').font.size = 11
        sheet_overview.range('A1').font.bold = True
        sheet_overview.range('B:B').number_format = '#,##0.00'
        sheet_overview.range('A:B').api.HorizontalAlignment = xlw.constants.HAlign.xlHAlignCenter
        sheet_overview.range('A2').number_format = 'yyyy/m/d'
        sheet_overview.range('B12').number_format = '#,##0.************'

        sheet_overview.autofit()

        print('----------------------------------------')

        print('Saving the valuation report ({} - {})...'.format(fund_name, report_date))
        internal_valuation_report_path = valuation_report_path + 'Valuation Report——ZOAMI - CLIENT ACCOUNT A_Daily Estimate NAV_' + report_date + '.xlsx'
        wb.save(internal_valuation_report_path)

        # input('Please update the column K "持有期间利息" in the sheet "交易流水" of the internal valuation report file, then press RETURN to continue...')

    # with xlw.App(visible=True) as app:
        client_wb = app.books.add()
        # with xlw.Book(internal_valuation_report_path, visible=True) as wb:
        sheets_to_copy = ['交易流水_MBS', '交易明细列表_MBS', '持仓报告', '交易流水', '交易明细列表', '债券持仓明细', '账户整体概况']
        for sheet_name in sheets_to_copy:
            source_sheet = wb.sheets[sheet_name]
            source_sheet.activate()
            source_sheet.api.Select()
            # new_sheet = client_wb.sheets.add(sheet_name, after=client_wb.sheets[-1])
            # new_sheet.range('A1').value = source_sheet.range('A1').expand().value
            source_sheet.api.Copy(Before=client_wb.sheets[0].api)
        # 处理新文件中的 Sheet：公式转值
        for sheet_name in sheets_to_copy:
            new_sheet = client_wb.sheets[sheet_name]
            # 获取所有已用区域的值（包含公式结果）
            values = new_sheet.used_range.value
            # 覆盖公式为静态值
            new_sheet.used_range.value = values
        client_wb.sheets[len(sheets_to_copy)].delete()

        client_valuation_report_path = valuation_report_path + 'Valuation Report——ZOAMI - CLIENT ACCOUNT A_Daily Estimate NAV_' + report_date + '_New Template.xlsx'
        client_wb.save(client_valuation_report_path)

    if perform_previous_report_comparison == 'yes':
        previous_report_file = find_latest_file_before_date(history_report_path, report_regex, report_date)
        if not previous_report_file:
            print('No previous valuation report found!')
            print('Valuation report check failure!')
            print('----------------------------------------')
        else:
            print('Comparing the valuation report ({} - {})...'.format(fund_name, report_date))
            previous_report_wb = xlw.Book(previous_report_file)
            current_client_report_wb = xlw.Book(client_valuation_report_path)
            current_internal_report_wb = xlw.Book(internal_valuation_report_path)
            current_internal_report_wb.app.calculate()

            # 创建新的workbook用于记录检查结果
            wb = xlw.Book()
            sheets_to_copy = ['持仓报告', '账户整体概况']
            for sheet_name in sheets_to_copy:
                source_sheet = current_client_report_wb.sheets[sheet_name]
                source_sheet.activate()
                source_sheet.api.Select()
                source_sheet.api.Copy(Before=wb.sheets[0].api)

            # 检查客户报告中是否存在#N/A
            na_sheet = wb.sheets.add('NA_check_result')
            # na_check_sheet_list = ['账户整体概况', '债券持仓明细', '交易明细列表', '交易流水', '持仓报告', '交易明细列表_MBS', '交易流水_MBS']

            na_check_sheet_list = [item.strip() for item in na_check_sheet_list.split(',')]
            error_count = check_na_errors(current_internal_report_wb, na_check_sheet_list, na_sheet)
            if error_count > 0:
                print(Fore.RED + f"Total #N/A errors found: {error_count}" + Style.RESET_ALL)
                print(Fore.YELLOW + "Errors have been recorded in NA_sheet" + Style.RESET_ALL)
            else:
                na_sheet.range('A1').value = 'No #N/A errors found!'
                print(Fore.GREEN + "No #N/A errors found!" + Style.RESET_ALL)


            # 检查概要sheet
            check_failure = False

            previous_sheet_overview = previous_report_wb.sheets['账户整体概况']
            current_sheet_overview = wb.sheets['账户整体概况']

            previous_account_amount_by_cost = previous_sheet_overview.range('B4').value
            current_account_amount_by_cost = current_sheet_overview.range('B4').value
            if previous_account_amount_by_cost == 0:
                current_sheet_overview.range('C4').value = 'N/A'
            else:
                current_sheet_overview.range('C4').value = f'{(current_account_amount_by_cost / previous_account_amount_by_cost - 1) * 100:.2f}%'

            previous_repo_amount = previous_sheet_overview.range('B5').value
            current_repo_amount = current_sheet_overview.range('B5').value
            if previous_repo_amount == 0:
                current_sheet_overview.range('C5').value = 'N/A'
            else:
                current_sheet_overview.range('C5').value = f'{(current_repo_amount / previous_repo_amount - 1) * 100:.2f}%'

            previous_interest_amount = previous_sheet_overview.range('B8').value
            current_interest_amount = current_sheet_overview.range('B8').value
            if previous_interest_amount == 0:
                current_sheet_overview.range('C8').value = 'N/A'
            else:
                current_sheet_overview.range('C8').value = f'{(current_interest_amount / previous_interest_amount - 1) * 100:.2f}%'

            previous_mgt_fee = previous_sheet_overview.range('B10').value
            current_mgt_fee = current_sheet_overview.range('B10').value
            if previous_mgt_fee == 0:
                current_sheet_overview.range('C10').value = 'N/A'
            else:
                current_sheet_overview.range('C10').value = f'{(current_mgt_fee / previous_mgt_fee - 1) * 100:.2f}%'

            previous_custody_fee = previous_sheet_overview.range('B11').value
            current_custody_fee = current_sheet_overview.range('B11').value
            if previous_custody_fee == 0:
                current_sheet_overview.range('C11').value = 'N/A'
            else:
                current_sheet_overview.range('C11').value = f'{(current_custody_fee / previous_custody_fee - 1) * 100:.2f}%'

            previous_nav_by_cost = previous_sheet_overview.range('B13').value
            current_nav_by_cost = current_sheet_overview.range('B13').value
            if previous_nav_by_cost == 0:
                current_sheet_overview.range('C13').value = 'N/A'
            else:
                current_sheet_overview.range('C13').value = f'{(current_nav_by_cost / previous_nav_by_cost - 1) * 100:.2f}%'

            previous_account_amount_by_market_value = previous_sheet_overview.range('B21').value
            current_account_amount_by_market_value = current_sheet_overview.range('B21').value
            if previous_account_amount_by_market_value == 0:
                current_sheet_overview.range('C21').value = 'N/A'
            else:
                current_sheet_overview.range('C21').value = f'{(current_account_amount_by_market_value / previous_account_amount_by_market_value - 1) * 100:.2f}%'

            previous_repo_amount = previous_sheet_overview.range('B22').value
            current_repo_amount = current_sheet_overview.range('B22').value
            if previous_repo_amount == 0:
                current_sheet_overview.range('C22').value = 'N/A'
            else:
                current_sheet_overview.range('C22').value = f'{(current_repo_amount / previous_repo_amount - 1) * 100:.2f}%'

            previous_interest_amount = previous_sheet_overview.range('B25').value
            current_interest_amount = current_sheet_overview.range('B25').value
            if previous_interest_amount == 0:
                current_sheet_overview.range('C25').value = 'N/A'
            else:
                current_sheet_overview.range('C25').value = f'{(current_interest_amount / previous_interest_amount - 1) * 100:.2f}%'

            previous_mgt_fee = previous_sheet_overview.range('B27').value
            current_mgt_fee = current_sheet_overview.range('B27').value
            if previous_mgt_fee == 0:
                current_sheet_overview.range('C27').value = 'N/A'
            else:
                current_sheet_overview.range('C27').value = f'{(current_mgt_fee / previous_mgt_fee - 1) * 100:.2f}%'

            previous_custody_fee = previous_sheet_overview.range('B28').value
            current_custody_fee = current_sheet_overview.range('B28').value
            if previous_custody_fee == 0:
                current_sheet_overview.range('C28').value = 'N/A'
            else:
                current_sheet_overview.range('C28').value = f'{(current_custody_fee / previous_custody_fee - 1) * 100:.2f}%'

            previous_nav_by_market_value = previous_sheet_overview.range('B29').value
            current_nav_by_market_value = current_sheet_overview.range('B29').value
            if previous_nav_by_market_value == 0:
                current_sheet_overview.range('C29').value = 'N/A'
            else:
                current_sheet_overview.range('C29').value = f'{(current_nav_by_market_value / previous_nav_by_market_value - 1) * 100:.2f}%'






            previous_report_wb.close()
            current_client_report_wb.close()
            current_internal_report_wb.close()

            # 保存检查报告
            check_report_path = check_result_path + f"Check_Report_{dt.datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
            wb.save(check_report_path)
            wb.close()
            print(Fore.GREEN + f"Check report saved as: {check_report_path}" + Style.RESET_ALL)

            print('Valuation Report Created Successfully!')
            print_warning('Please don''t forget to update the column K "持有期间利息" in the sheet "交易流水" of the internal valuation report file! Please press RETURN to continue...')
            input('Please press RETURN to continue...')

    else:
        print('Valuation Report Created Successfully!')
        print('----------------------------------------')
        input('Please don''t forget to update the column K "持有期间利息" in the sheet "交易流水" of the internal valuation report file! Please press RETURN to continue...')