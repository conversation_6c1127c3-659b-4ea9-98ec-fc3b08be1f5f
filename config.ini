[valuation_report]
db_server = 127.0.0.1
#sqlite3, mysql, mssql
db_type = sqlite3
#fund name
fund_name = client_account_a
#trade record path
trade_record_path = .\\trade_record_data\\trade_record\\
#repo trade record path
repo_trade_record_path = .\\trade_record_data\\repo_trade_record\\
#trade record mbs path
trade_record_mbs_path = .\\trade_record_data\\trade_record_mbs\\
#trade record bbg data path (Static BBG data file by BBG Macro)
bba_data_trade_record_path = .\\bba_data\\bba_data_trade_record.xlsx
#repo trade record bbg data path (Static BBG data file by BBG Macro)
bba_data_repo_trade_record_path = .\\bba_data\\bba_data_repo_trade_record.xlsx
#trade record mbs bbg data path (Static BBG data file by BBG Macro)
bba_data_trade_record_mbs_path = .\\bba_data\\bba_data_trade_record_mbs.xlsx
#classification data path
classification_data_path = .\\classification_data\\classification_data.xlsx
#BBG Macro App path
bba_data_macro_app_path = .\\get_bba_data.xlsm
#valuation report path
valuation_report_path = .\\valuation_report\\
cash_movement_path = .\\cash_movement\\cash_movement.xlsx
perform_previous_report_comparison = yes
history_report_path = \\192.168.10.21\\department folders\\Fund Operations Folder\\Fund Products\\********* CLIENT ACCOUNT A - Bohai Bank\\Daily indicative NAV\\To Client - without formula\\
report_regex = ^Valuation Report—ZOAMI - CLIENT ACCOUNT A_Daily Estimate NAV_{target_date}_New Template\.(XLSX|xlsx|XLS|xls)$
check_result_path = .\\check_result\\
na_check_sheet_list = '账户整体概况','债券持仓明细','交易明细列表','交易流水','持仓报告','交易明细列表_MBS','交易流水_MBS'

[reconciliation_holding]
#custodian jpm file password
jpm_repo_file_password = ,KV42l)hSM
#bbg pm holdingpath (holding data from Bloomberg)
bbg_pm_holding_path = .\\holding_data\\
#cmbc holding path
cmbc_holding_path = .\\holding_data\\
#jpm repo holding path
jpm_repo_holding_path = .\\holding_data\\
#standard charted repo holding path
sc_repo_holding_path = .\\holding_data\\
#bbg pm holding file name regex
bbg_pm_filename_regex = ^BBG_PM_\d+\.xlsx$
#cmbc holding file name regex
cmbc_filename_regex = ^\d+_*********_sec_holding\.xlsx$
#sc repo holding file name regex
sc_repo_filename_regex = ^CounterpartyTradeExposureReport_\d+\.csv$
#jpm repo holding file name regex
jpm_repo_filename_regex = ^MarginStatement_A133958343_\d+\.xlsx$
#repo holding file name regex
reconciliation_report_path = .\\reconciliation_report\\
