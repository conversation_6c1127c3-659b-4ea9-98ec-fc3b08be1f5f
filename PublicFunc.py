import numpy as np
import pandas as pd
import os
import sys
from threading import Timer
from datetime import *
import datetime as dt
import time
import json
import xlwings as xlw
import calendar
from sqlalchemy import *
from pyDes import *
import base64
# import hashlib
# from Crypto.Hash import SHA1
# from Crypto.Signature import pkcs1_15
# from Crypto.PublicKey import RSA
# from Crypto.Cipher import AES
import re
import smtplib
from email.mime.text import MIMEText  # 发送邮件内容为文本形式时导入
from email.header import Header  # 给邮件设置标题时导入
import email
import mimetypes
from email.mime.multipart import MIMEMultipart
from email.mime.image import MIMEImage
from email.mime.base import MIMEBase
import configparser
import warnings

Des_Key = "UBPSHNAV"  # Key
Des_IV = "46238239"  # 自定IV向量

PER_PAGE = 50

glb_assign_date = ''
glb_fund_name = ''
glb_customer_name = ''
glb_sales_name = ''
glb_table_data = []
ALLOWED_EXTENSIONS = {'csv', 'xlsx'}
INDUSTRY_NEUTRAL_ALLOWED_EXTENSIONS = {'csv', 'xlsx', 'zip'}
VALUATION_STATEMENT_ALLOWED_EXTENSIONS = {'zip'}
VALUATION_MAPPING_ALLOWED_EXTENSIONS = {'xlsx'}
table_columns = ['Date', 'Fund Name', 'Customer Name', 'Sales Name']

# 缓存配置
# 设置缓存时间为 60 秒
cache_timeout = 60
cache = {}

os.environ["PYTHONUTF8"] = "1"
# sys.stdout = sys.__stdout__

def DesDecrypt(encode, key=None, iv=None):
    if key is None:
        key = Des_Key
    if iv is None:
        iv = Des_IV

    k = des(key, CBC, iv, pad=None, padmode=PAD_PKCS5)
    b64str = base64.b64decode(encode)
    return k.decrypt(b64str)


def isNaNo(sth):
    '''
    NaN、None或者空字符串返回True，其他情况返回False
    '''
    if sth is None:
        return True
    if isinstance(sth, float):
        if np.isnan(sth):
            return True
    return False


def is_valid_date(date_str, date_format="%Y-%m-%d"):
    try:
        # 尝试将输入的字符串解析为日期
        dt.datetime.strptime(date_str, date_format)
        return True
    except ValueError:
        return False


def allowed_file(filename):
    return '.' in filename and \
        filename.rsplit('.', 1)[1] in ALLOWED_EXTENSIONS


def industry_neutral_allowed_file(filename):
    return '.' in filename and \
        filename.rsplit('.', 1)[1] in INDUSTRY_NEUTRAL_ALLOWED_EXTENSIONS


def valuation_statement_allowed_file(filename):
    return '.' in filename and \
        filename.rsplit('.', 1)[1] in VALUATION_STATEMENT_ALLOWED_EXTENSIONS


def valuation_mapping_allowed_file(filename):
    return '.' in filename and \
        filename.rsplit('.', 1)[1] in VALUATION_MAPPING_ALLOWED_EXTENSIONS


def days_count_of_year(years):
    return 366 if calendar.isleap(int(str(years))) else 365


def days_between_date(begin_date, finish_date):
    date_list = []
    begin_date = dt.datetime.strptime(begin_date, "%Y-%m-%d")
    finish_date = dt.datetime.strptime(finish_date, "%Y-%m-%d")
    while begin_date <= finish_date:
        date_str = begin_date.strftime("%Y-%m-%d")
        date_list.append(date_str)
        begin_date += dt.timedelta(days=1)
    return date_list


def count_between_date(day1, day2):
    time_array1 = time.strptime(day1, "%Y-%m-%d")
    timestamp_day1 = int(time.mktime(time_array1))
    time_array2 = time.strptime(day2, "%Y-%m-%d")
    timestamp_day2 = int(time.mktime(time_array2))
    result = (timestamp_day2 - timestamp_day1) // 60 // 60 // 24
    return result


def get_last_day_of_previous_month(current_date):
    current_date = dt.datetime.strptime(current_date, "%Y-%m-%d")

    # 计算上一个月的第一天
    first_day_of_this_month = current_date.replace(day=1)
    last_day_of_previous_month = first_day_of_this_month - timedelta(days=1)

    # 返回上个月的最后一天
    return last_day_of_previous_month


def find_rows_in_sheet_openpyxl(find_str, ws, col, start_row, end_row):
    for row in range(start_row, end_row + 1):
        cell = ws.cell(row=row, column=col)  # 第二列的列号为2
        if str(find_str) in str(cell.value).strip():
            # 如果找到包含 'abc' 的单元格，记录行号
            return row
    # 如果没有找到，返回 None
    return None


def find_rows_in_sheet_xlrd(find_str, ws, col, start_row, end_row):
    for row in range(start_row, end_row + 1):
        if str(find_str) in str(ws.cell_value(row, col)).strip():
            # 如果找到包含 'abc' 的单元格，记录行号
            return row
    # 如果没有找到，返回 None
    return None


def find_cols_in_sheet_openpyxl(find_str, ws, row, start_col, end_col):
    for col in range(start_col, start_col + 1):
        cell = ws.cell(row=row, column=col)  # 第二列的列号为2
        if str(find_str) in str(cell.value).strip():
            # 如果找到包含 'abc' 的单元格，记录行号
            return col
    # 如果没有找到，返回 None
    return None


def find_cols_in_sheet_xlrd(find_str, ws, row, start_col, end_col):
    for col in range(start_col, start_col + 1):
        if str(find_str) in str(ws.cell_value(row, col)).strip():
            # 如果找到包含 'abc' 的单元格，记录行号
            return col
    # 如果没有找到，返回 None
    return None


def get_last_cell_in_column_openpyxl(ws, col):
    # 转换列的数字索引为字母
    # column_letter = openpyxl.utils.get_column_letter(column_index)
    max_row = ws.max_row
    while max_row > 0 and ws[f"{col}{max_row}"] is None:
        max_row -= 1

    # 如果 max_row 是 0，意味着整列都是空的
    if max_row == 0:
        return None
    else:
        return max_row


def get_last_cell_in_column_xlrd(ws, col):
    # 转换列的数字索引为字母
    # column_letter = openpyxl.utils.get_column_letter(column_index)
    max_row = ws.nrows - 1
    while max_row > 0:
        cell_value = ws.cell_value(max_row, col)
        if cell_value != '':
            break
        max_row -= 1

    # 如果 max_row 是 0，意味着整列都是空的
    if max_row == 0:
        return None
    else:
        return max_row


def hex_to_rgb(hex_color):
    hex_color = hex_color.lstrip('#')
    return tuple(int(hex_color[i:i + 2], 16) for i in (0, 2, 4))


def sendEmail(authInfo, fromAdd, toAdd, subject, plainText, htmlText, attPath):
    strFrom = fromAdd
    strTo = ', '.join(toAdd)

    server = authInfo.get('server')
    user = authInfo.get('user')
    passwd = authInfo.get('password')

    if not (server and user and passwd):
        print
        'incomplete login info, exit now'
        return

    # 设定root信息
    msgRoot = MIMEMultipart('related')
    msgRoot['Subject'] = subject
    msgRoot['From'] = strFrom
    msgRoot['To'] = strTo
    msgRoot.preamble = 'This is a multi-part message in MIME format.'

    # Encapsulate the plain and HTML versions of the message body in an
    # 'alternative' part, so message agents can decide which they want to display.
    msgAlternative = MIMEMultipart('alternative')
    msgRoot.attach(msgAlternative)

    # 设定纯文本信息
    msgText = MIMEText(plainText, 'plain', 'utf-8')
    msgAlternative.attach(msgText)

    # 设定HTML信息
    msgText = MIMEText(htmlText, 'html', 'utf-8')
    msgAlternative.attach(msgText)

    # # 设定内置图片信息
    # fp = open('test.jpg', 'rb')
    # msgImage = MIMEImage(fp.read())
    # fp.close()
    # msgImage.add_header('Content-ID', '<image1>')
    # msgRoot.attach(msgImage)

    if attPath != "":
        # send_file = open(attPath, "rb").read()
        #
        # att = MIMEText(send_file, "base64", 'utf-8')
        # att['Content-Type'] = 'application/octet-stream'
        # attPathSplit = attPath.split('\\')
        # att['Content-Disposition'] = 'attachment;filename=' + os.path.basename(attPathSplit[len(attPathSplit) - 1])
        # msgRoot.attach(att)

        # 构造MIMEBase对象做为文件附件内容并附加到根容器
        contype = 'application/octet-stream'
        maintype, subtype = contype.split('/', 1)

        ## 读入文件内容并格式化
        data = open(attPath, 'rb')
        file_msg = MIMEBase(maintype, subtype)
        file_msg.set_payload(data.read())
        data.close()
        email.encoders.encode_base64(file_msg)

        ## 设置附件头
        basename = os.path.basename(attPath)
        file_msg.add_header('Content-Disposition',
                            'attachment', filename=basename)
        msgRoot.attach(file_msg)

    # 发送邮件
    smtp = smtplib.SMTP(server)
    # 设定调试级别，依情况而定
    # smtp.set_debuglevel(1)
    smtp.connect(server, 25)
    # smtp.login(user, passwd)
    smtp.sendmail(strFrom, toAdd, msgRoot.as_string())
    smtp.quit()
    return


def calculate_text_width(text):
    # 中文字符宽度为2，其他字符宽度为1
    chinese_pattern = re.compile(r'[\u4e00-\u9fa5\u3000-\u303F\uFF01-\uFFEF]')
    width = 0
    for char in text:
        if chinese_pattern.match(char):
            width += 2
        else:
            width += 1
    return width


def find_latest_file(folder_path, regex):
    pattern = re.compile(regex.lower())
    latest_time = 0
    latest_file = None
    matched_files = []

    for root, dirs, files in os.walk(folder_path):
        for filename in files:
            if pattern.match(filename.lower()):
                file_path = os.path.join(root, filename)
                matched_files.append(filename)
                # timestamp = os.path.getmtime(file_path)
                # if timestamp > latest_time:
                #     latest_time = timestamp
                #     latest_file = file_path

    if not matched_files:
        return None

    matched_files.sort(reverse=True)
    max_filename = matched_files[0]

    # return latest_file
    return max_filename


def find_latest_file_before_date(folder_path, regex, target_date):
    """
    查找指定路径下符合正则表达式且文件名中的日期不大于等于目标日期的最新文件

    参数：
        folder_path (str): 要搜索的文件夹路径
        regex (str): 文件名匹配的正则表达式，可包含{target_date}占位符
        target_date (str): 目标日期，格式为'YYYYMMDD'

    返回：
        str: 找到的文件路径，如果没找到则返回None
    """
    # 将YYYYMMDD格式转换为datetime对象
    target_date = pd.to_datetime(target_date).strftime('%Y%m%d')
    target_date_obj = dt.datetime.strptime(target_date, "%Y%m%d")

    # 将正则表达式中的日期占位符替换为通配符
    # 例如：^H0ZOFI_SR1000C01A_{target_date}_1_164009\.CSV$
    # 变为：^H0ZOFI_SR1000C01A_\d{8}_1_164009\.CSV$
    generic_regex = regex.replace("{target_date}", r"\d{8}")
    pattern = re.compile(generic_regex, re.IGNORECASE)

    matched_files = []

    # 搜索所有匹配的文件
    for root, dirs, files in os.walk(folder_path):
        for filename in files:
            if pattern.match(filename):
                # 从文件名中提取日期
                file_date_str = extract_date(filename)
                if file_date_str:
                    file_date_obj = dt.datetime.strptime(file_date_str, "%Y%m%d")
                    # 只保留日期不大于目标日期的文件
                    if file_date_obj < target_date_obj:
                        file_path = os.path.join(root, filename)
                        matched_files.append((file_date_obj, file_path))

    if not matched_files:
        return None

    # 按日期降序排序，取第一个（最新的）
    matched_files.sort(key=lambda x: x[0], reverse=True)
    return matched_files[0][1]  # 返回文件路径


def extract_date(s):
    # 匹配字符串中所有8位连续数字（如20250307）
    possible_dates = re.findall(r'\d{8}', s)
    for date_str in possible_dates:
        try:
            # 验证是否为合法日期（格式YYYYMMDD）
            datetime.strptime(date_str, "%Y%m%d")
            return date_str  # 返回第一个有效日期
        except ValueError:
            continue
    return None


def print_alert(message):
    print('\033[91m' + message + '\033[0m')


def print_reminder(message):
    print('\033[92m' + message + '\033[0m')

def print_warning(message):
    print('\033[93m' + message + '\033[0m')



# 创建 ConfigParser 对象
config = configparser.ConfigParser()
# 读取配置文件
config.read('config.ini', encoding='utf-8')

config_cat = 'valuation_report'
db_server = config.get(config_cat, 'db_server')
db_type = config.get(config_cat, 'db_type')
fund_name = config.get(config_cat, 'fund_name')
bba_data_trade_record_path = config.get(config_cat, 'bba_data_trade_record_path')
bba_data_repo_trade_record_path = config.get(config_cat, 'bba_data_repo_trade_record_path')
bba_data_trade_record_mbs_path = config.get(config_cat, 'bba_data_trade_record_mbs_path')
classification_data_path = config.get(config_cat, 'classification_data_path')
trade_record_path = config.get(config_cat, 'trade_record_path')
repo_trade_record_path = config.get(config_cat, 'repo_trade_record_path')
trade_record_mbs_path = config.get(config_cat, 'trade_record_mbs_path')
bba_data_macro_app_path = config.get(config_cat, 'bba_data_macro_app_path')
valuation_report_path = config.get(config_cat, 'valuation_report_path')
cash_movement_path = config.get(config_cat, 'cash_movement_path')
history_report_path = config.get(config_cat, 'history_report_path')
report_regex = config.get(config_cat, 'report_regex')
perform_previous_report_comparison = config.get(config_cat, 'perform_previous_report_comparison')
check_result_path = config.get(config_cat, 'check_result_path')

config_cat = 'reconciliation_holding'
jpm_repo_file_password = config.get(config_cat, 'jpm_repo_file_password')
bbg_pm_holding_path = config.get(config_cat, 'bbg_pm_holding_path')
cmbc_holding_path = config.get(config_cat, 'cmbc_holding_path')
jpm_repo_holding_path = config.get(config_cat, 'jpm_repo_holding_path')
sc_repo_holding_path = config.get(config_cat, 'sc_repo_holding_path')
bbg_pm_filename_regex = config.get(config_cat, 'bbg_pm_filename_regex')
cmbc_filename_regex = config.get(config_cat, 'cmbc_filename_regex')
sc_repo_filename_regex = config.get(config_cat, 'sc_repo_filename_regex')
jpm_repo_filename_regex = config.get(config_cat, 'jpm_repo_filename_regex')
reconciliation_report_path = config.get(config_cat, 'reconciliation_report_path')

# db_fmsdb = engine.URL.create(
#     drivername='mssql',
#     username='fms_admin',
#     password=DesDecrypt("JEQygoEP3kNi4gXNGz5ydQ==").decode('utf-8'),
#     host=db_server,
#     port='1433',
#     database='FMSDB',
#     query={'driver': "ODBC Driver 11 for SQL Server"}
# )
# mssql_engine = create_engine(db_fmsdb, echo=False, fast_executemany=True, use_setinputsizes=False)

# db_marketdb = engine.URL.create(
#     drivername='mssql',
#     username='marketdb-reader',
#     password=DesDecrypt("1NKyHYixq9WjTbhIxyAqDg==").decode('utf-8'),
#     host=db_server,
#     port='1433',
#     database='MarketDB',
#     query={'driver': "ODBC Driver 11 for SQL Server"}
# )


class MSSQL_DBHandler:
    def __init__(self, connection_string):
        """
        初始化SQL Server操作类。

        :param connection_string: SQLAlchemy支持的连接字符串，例如：
                                  "mssql+pyodbc://username:password@server/database?driver=ODBC+Driver+17+for+SQL+Server"
        """
        self.connection_string = connection_string
        connection_string = engine.URL.create(
            drivername='mssql',
            username='fms_admin',
            password=DesDecrypt("JEQygoEP3kNi4gXNGz5ydQ==").decode('utf-8'),
            host=db_server,
            port='1433',
            database='FMSDB',
            query={'driver': "ODBC Driver 11 for SQL Server"}
        )
        self.engine = create_engine(connection_string, echo=False, fast_executemany=True, use_setinputsizes=False)

    def db_engine(self):
        return self.engine

    def query(self, sql_query, params=None):
        """
        执行SQL查询并返回结果为DataFrame。

        :param sql_query: 查询SQL语句。
        :param params: 可选的查询参数，字典形式。
        :return: 查询结果，pandas.DataFrame
        """
        try:
            with self.engine.connect() as connection:
                df = pd.read_sql_query(text(sql_query), connection, params=params)
                # print(df)
                # sys.stdout.flush()
            return df
        except Exception as e:
            print(f"查询失败: {e}")
            return None

    def execute_sql(self, sql_statement, params=None):
        """
        执行任意SQL语句（包括插入、更新、删除等）。

        :param sql_statement: 完整的SQL语句。
        :param params: 可选的SQL参数，字典形式。
        """
        try:
            with self.engine.begin() as connection:
                connection.execute(text(sql_statement), params or {})
            # print("SQL语句执行成功！")
        except Exception as e:
            print(f"SQL语句执行失败: {e}")


class SQLITE3_DBHandler:
    def __init__(self):
        """
        初始化SQL Server操作类。

        :param connection_string
        """
        db_path = 'fmsdb.db'
        connection_string = 'sqlite:///' + db_path
        self.engine = create_engine(connection_string, echo=False)

    def db_engine(self):
        return self.engine

    def query(self, sql_query, params=None):
        """
        执行SQL查询并返回结果为DataFrame。

        :param sql_query: 查询SQL语句。
        :param params: 可选的查询参数，字典形式。
        :return: 查询结果，pandas.DataFrame
        """
        try:
            with self.engine.connect() as connection:
                df = pd.read_sql_query(text(sql_query), connection, params=params)
                # print(df)
                # sys.stdout.flush()
            return df
        except Exception as e:
            print(f"查询失败: {e}")
            return None

    def execute_sql(self, sql_statement, params=None):
        """
        执行任意SQL语句（包括插入、更新、删除等）。

        :param sql_statement: 完整的SQL语句。
        :param params: 可选的SQL参数，字典形式。
        """
        try:
            with self.engine.begin() as connection:
                connection.execute(text(sql_statement), params or {})
            # print("SQL语句执行成功！")
        except Exception as e:
            print(f"SQL语句执行失败: {e}")


class MyTimer(object):
    def __init__(self, start_time, interval, callback_proc, args=None, kwargs=None):
        self.__timer = None
        self.__start_time = start_time
        self.__interval = interval
        self.__callback_pro = callback_proc
        self.__args = args if args is not None else []
        self.__kwargs = kwargs if kwargs is not None else {}

    def exec_callback(self, args=None, kwargs=None):
        self.__callback_pro(*self.__args, **self.__kwargs)
        self.__timer = Timer(self.__interval, self.exec_callback)
        self.__timer.start()

    def start(self):
        # interval = self.__interval - (datetime.now().timestamp() - self.__start_time.timestamp())
        interval = self.__start_time.timestamp() - datetime.now().timestamp()
        print(interval)
        if interval < 0:
            print("目标开始时间小于当前时间，请重新设置！")
            sys.exit(0)
        self.__timer = Timer(interval, self.exec_callback)
        self.__timer.start()

    def cancel(self):
        self.__timer.cancel()
        self.__timer = None


class Utility(object):
    def num_to_char(self, num):
        """数字转中文"""
        num = str(num)
        new_str = ""
        num_dict = {"0": u"零", "1": u"一", "2": u"二", "3": u"三", "4": u"四", "5": u"五", "6": u"六", "7": u"七", "8": u"八", "9": u"九"}
        list_num = list(num)
        # print(list_num)
        shu = []
        for i in list_num:
            # print(num_dict[i])
            shu.append(num_dict[i])
        new_str = "".join(shu)
        # print(new_str)
        return new_str

    def to_long_date(self, date_str):
        return dt.datetime.strptime(date_str, "%Y%m%d").strftime('%Y-%m-%d')

    def to_short_date(self, date_str):
        return dt.datetime.strptime(date_str, "%Y-%m-%d").strftime('%Y%m%d')

    def resample_weekly(self, df, trading_days):
        df = df.set_index('trade_date')

        # 按周重采样（这里先按每天重采样成日线数据）
        df_resampled = df.resample('D').last()

        last_row_no = len(df_resampled) - 1

        trading_days['trade_date'] = pd.to_datetime(trading_days['trade_date'], format='%Y-%m-%d')
        trading_days['trade_date'] = trading_days['trade_date'].apply(lambda x: x.strftime('%Y-%m-%d'))

        trading_days_list = trading_days.values

        last_trading_day = None
        df_resampled_list = []

        index_no = 0

        for timestamp, row in df_resampled.iterrows():
            # 不是交易日,但是在nav_df中存在(因为nav_df做了日扩展)
            if str(timestamp.date()) not in trading_days_list:
                if timestamp.weekday() == 4 and last_trading_day is not None:
                    df_resampled_list.append(df_resampled.loc[last_trading_day])
                    last_trading_day = None

            else:  # 是交易日
                # 如果是当前周的最后一个交易日，则将当前行保存到df_resampled_list中，
                # 否则把current_date赋值为当前行的索引
                if timestamp.weekday() == 4:
                    df_resampled_list.append(row)
                else:  # 记录非周五的时间序号
                    if index_no == last_row_no:
                        df_resampled_list.append(row)
                    else:
                        last_trading_day = timestamp
            index_no += 1

        # 合并保存在df_resampled_list中的重采样数据
        df_resampled = pd.concat(df_resampled_list, axis=1).T
        df_resampled.reset_index(inplace=True)
        df_resampled.rename(columns={'index': 'trade_date'}, inplace=True)

        return df_resampled

    def coordinate_to_number(coordinate):
        letter = coordinate[:-1]  # 提取字母部分
        row = int(coordinate[-1])  # 提取数字部分

        column = 0
        for char in letter:
            column = column * 26 + (ord(char.upper()) - 64)  # 转换字母为列数

        return column, row

    def excel_to_pdf(self, excel_file_name, saved_pdf_file_name):
        # xlApp = Dispatch("Excel.Application")
        # xlApp.Visible = False
        # xlApp.DisplayAlerts = 0
        # wb = xlApp.Workbooks.Open(excel_file_name, False)
        # sheet_list = ['Sheet1']  # say you want to print these sheets
        # wb.WorkSheets(sheet_list).Select()
        # wb.ActiveSheet.ExportAsFixedFormat(0, saved_pdf_file_name)
        # pyperclip.copy('')
        # wb.Close(False)
        # wb.Close(False)
        # xlApp.Quit()
        # xlApp = None
        wb = xlw.Book(excel_file_name)
        sheet = wb.sheets['Sheet1']  # 获取指定的工作表对象
        try:
            sheet.api.ExportAsFixedFormat(0, saved_pdf_file_name)  # 打印指定的工作表
        except Exception as e:
            print("Error:", str(e))
        finally:
            wb.close()

