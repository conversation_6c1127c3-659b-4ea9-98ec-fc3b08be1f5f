import pandas as pd

from PublicFunc import *

if __name__ == '__main__':
    while True:
        user_input = input("Please input report date(YYYY-MM-DD): ")
        if not user_input:  # 如果用户没有输入任何内容
            print("No date input. The current date will be used as default date{}".format(dt.datetime.today().strftime('%Y-%m-%d')))
            report_date = dt.datetime.today().strftime('%Y-%m-%d')
            break
        if is_valid_date(user_input):
            report_date = pd.to_datetime(user_input).strftime('%Y-%m-%d')
            break
        else:
            print("Invalid date format. Please try again.")

    if db_type == 'sqlite3':
        db = SQLITE3_DBHandler()
    else:
        db = MSSQL_DBHandler()

    # fund_name = 'client_account_a'
    # trade_record_path = './trade_record_data/trade_record/'
    # repo_trade_record_path = './trade_record_data/repo_trade_record/'
    # trade_record_mbs_path = './trade_record_data/trade_record_mbs/'

    for filename in os.listdir(trade_record_path):
        print('Importing {} trade record - {}'.format(fund_name, filename))

        file_path = os.path.join(trade_record_path, filename)

        df = pd.read_excel(file_path, index_col=None,
                           names=['as_date', 'settle_date', 'trade_ticket', 'trade_account', 'trade_direction', 'isin_code', 'sec_desc', 'trade_currency', 'trade_amount', 'trade_price',
                                  'accrued_interest', 'settle_total', 'level1_tag_name', 'firm_broker', 'trade_type', 'settle_location', 'trade_yield'],
                           usecols=[0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16], parse_dates=True, header=None, skiprows=1)

        if df.empty:
            break

        df['as_date'] = pd.to_datetime(df['as_date'], errors='coerce')
        df['as_date'] = df['as_date'].apply(lambda x: dt.datetime.strftime(x, '%Y-%m-%d'))
        df['settle_date'] = pd.to_datetime(df['settle_date'], errors='coerce')
        df['settle_date'] = df['settle_date'].apply(lambda x: dt.datetime.strftime(x, '%Y-%m-%d'))
        df.insert(0, 'fund_name', fund_name)

        date_seq = [f"{dt.datetime.now().strftime('%Y%m%d')}_{i+1}" for i in range(len(df))]
        df.insert(0, 'index_no', date_seq)

        ticket_list = df['trade_ticket'].tolist()
        ticket_list = ",".join(f"'{str(item)}'" for item in ticket_list)

        sql = "delete from bond_trade_record WHERE trade_ticket IN (" + ticket_list + ")"
        db.execute_sql(sql)

        df.to_sql('bond_trade_record', db.db_engine(), schema=None, if_exists='append', index=False, index_label=None, chunksize=None, dtype=None, method=None)

    for filename in os.listdir(repo_trade_record_path):
        print('Importing {} repo trade record - {}'.format(fund_name, filename))

        file_path = os.path.join(repo_trade_record_path, filename)

        df = pd.read_excel(file_path, index_col=None,
                           names=['as_date', 'settle_date', 'repo_termination_date', 'trade_ticket', 'trade_account', 'trade_direction', 'isin_code', 'sec_desc', 'trade_currency', 'haircut_fractional_indicator', 'repo_rate', 'trade_amount', 'trade_price',
                                  'repo_loan_amount', 'accrued_interest', 'settle_total', 'termination_money', 'level1_tag_name', 'firm_broker', 'trade_type', 'repo_term_type', 'repo_status', 'settle_location'],
                           usecols=[0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22], parse_dates=True, header=None, skiprows=1)

        if df.empty:
            break

        df['as_date'] = pd.to_datetime(df['as_date'], errors='coerce')
        df['as_date'] = df['as_date'].apply(lambda x: dt.datetime.strftime(x, '%Y-%m-%d'))
        df['settle_date'] = pd.to_datetime(df['settle_date'], errors='coerce')
        df['settle_date'] = df['settle_date'].apply(lambda x: dt.datetime.strftime(x, '%Y-%m-%d'))
        df['repo_termination_date'] = pd.to_datetime(df['repo_termination_date'], errors='coerce')
        df['repo_termination_date'] = df['repo_termination_date'].apply(lambda x: dt.datetime.strftime(x, '%Y-%m-%d') if pd.notna(x) else '')
        df.insert(0, 'fund_name', fund_name)

        date_seq = [f"{dt.datetime.now().strftime('%Y%m%d')}_{i + 1}" for i in range(len(df))]
        df.insert(0, 'index_no', date_seq)

        ticket_list = df['trade_ticket'].tolist()
        ticket_list = ",".join(f"'{str(item)}'" for item in ticket_list)

        sql = "delete from bond_repo_trade_record WHERE trade_ticket IN (" + ticket_list + ")"
        db.execute_sql(sql)

        df.to_sql('bond_repo_trade_record', db.db_engine(), schema=None, if_exists='append', index=False, index_label=None, chunksize=None, dtype=None, method=None)

    for filename in os.listdir(trade_record_mbs_path):
        print('Importing {} trade record mbs - {}'.format(fund_name, filename))

        file_path = os.path.join(trade_record_mbs_path, filename)

        df = pd.read_excel(file_path, index_col=None,
                           names=['as_date', 'settle_date', 'trade_ticket', 'trade_account', 'trade_direction', 'isin_code', 'sec_desc', 'trade_currency', 'trade_amount', 'trade_price',
                                  'accrued_interest', 'settle_total', 'level1_tag_name', 'firm_broker', 'trade_type', 'settle_location', 'trade_yield'],
                           usecols=[0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16], parse_dates=True, header=None, skiprows=1)

        if df.empty:
            break

        df['as_date'] = pd.to_datetime(df['as_date'], errors='coerce')
        df['as_date'] = df['as_date'].apply(lambda x: dt.datetime.strftime(x, '%Y-%m-%d'))
        df['settle_date'] = pd.to_datetime(df['settle_date'], errors='coerce')
        df['settle_date'] = df['settle_date'].apply(lambda x: dt.datetime.strftime(x, '%Y-%m-%d'))
        df.insert(0, 'fund_name', fund_name)

        date_seq = [f"{dt.datetime.now().strftime('%Y%m%d')}_{i + 1}" for i in range(len(df))]
        df.insert(0, 'index_no', date_seq)

        ticket_list = df['trade_ticket'].tolist()
        ticket_list = ",".join(f"'{str(item)}'" for item in ticket_list)

        sql = "delete from bond_trade_record_mbs WHERE trade_ticket IN (" + ticket_list + ")"
        db.execute_sql(sql)

        df.to_sql('bond_trade_record_mbs', db.db_engine(), schema=None, if_exists='append', index=False, index_label=None, chunksize=None, dtype=None, method=None)

    if db_type == 'sqlite3':
        db = SQLITE3_DBHandler()
    else:
        db = MSSQL_DBHandler()

    # 开始生成excel for bba
    sql = "select settle_date, trade_ticket, isin_code, trade_amount from bond_trade_record where fund_name = '" + fund_name + "' and as_date <= '" + report_date + "' order by settle_date"
    trade_record_df = db.query(sql)
    if not trade_record_df.empty:
        for df_index, df_row in trade_record_df.iterrows():
            trade_record_df['pm'] = trade_record_df['isin_code'] + ' @BVal Corp'
            trade_record_df['name_chinese_traditional'] = trade_record_df.apply(lambda row: f'=BDP(E{row.name + 4}, "name_chinese_traditional")', axis=1)
            trade_record_df['security_des'] = trade_record_df.apply(lambda row: f'=BDP(E{row.name + 4}, "security_des")', axis=1)
            trade_record_df['cpn'] = trade_record_df.apply(lambda row: f'=BDP(E{row.name + 4}, "cpn")', axis=1)
            trade_record_df['maturity'] = trade_record_df.apply(lambda row: f'=IF(S{row.name + 4}="#N/A Field Not Applicable",BDP(E{row.name + 4}, "maturity"), IF(T{row.name + 4}="TLAC",S{row.name + 4},BDP(E{row.name + 4}, "maturity")))', axis=1)
            trade_record_df['accrued_interest'] = trade_record_df.apply(lambda row: f'=BDP($C{row.name + 4} & " CORP","TKT_ACCRUED","SETTLE_DT",TEXT(MAX($E$2+1,$A{row.name + 4}),"YYYYMMDD"),"BQ_FACE_AMT",$D{row.name + 4})', axis=1)
            trade_record_df['ai_coupon_date'] = trade_record_df.apply(lambda row: f'=IF($J{row.name + 4}=0, BDP($C{row.name + 4} & " CORP","TKT_ACCRUED","SETTLE_DT",TEXT(MAX($E$2,$A{row.name + 4}),"YYYYMMDD"),"BQ_FACE_AMT",$D{row.name + 4}), 0)', axis=1)
            trade_record_df['px_mid'] = trade_record_df.apply(lambda row: f'=IF($E$2=TODAY(), BDP($E{row.name + 4}, "px_mid"), BDH($E{row.name + 4}, "px_mid", $E$2))', axis=1)
            trade_record_df['int_acc'] = trade_record_df.apply(lambda row: f'=BDP($C{row.name + 4} & " ISIN", "int_acc", "SETTLE_DT",$E$2)', axis=1)
            # trade_record_df['加权收益率(%) - 市价估值'] = trade_record_df.apply(lambda row: f'=@BDP($C{row.name + 4} & " CORP","YAS_BOND_YLD","YAS_YLD_FLAG=15","YAS_BOND_PX",$L{row.name + 4})', axis=1)
            # trade_record_df['加权收益率(%) - 市价估值'] = trade_record_df.apply(lambda row: f'=IF(AND(OR(BDP(C{row.name + 4}&" Corp", "CAPITAL_TIER")="AT1",BDP(C{row.name + 4}&" Corp", "CAPITAL_TIER")="T2",BDP(C{row.name + 4}&" Corp", "BASEL_III_DESIGNATION")="TLAC"),BDP(C{row.name + 4}&" Corp", "HAS_CALL_FEATURE")="YES"),BDP(C{row.name + 4}&" Corp", "YAS_BOND_YLD", "YAS_YLD_FLAG=15", "YAS_BOND_PX", $L{row.name + 4}),BDP(C{row.name + 4}&" Corp", "YAS_BOND_YLD", "YAS_YLD_FLAG=2", "YAS_BOND_PX", $L{row.name + 4}))', axis=1)
            # trade_record_df['加权收益率(%) - 市价估值'] = trade_record_df.apply(lambda row: f'=IF(OR(BDP(C{row.name + 4}&" Corp", "CAPITAL_TIER")="AT1",BDP(C{row.name + 4}&" Corp", "CAPITAL_TIER")="T2",BDP(C{row.name + 4}&" Corp", "BASEL_III_DESIGNATION")="TLAC"),BDP(C{row.name + 4}&" Corp", "YAS_BOND_YLD", "YAS_YLD_FLAG=15", "YAS_BOND_PX", $L{row.name + 4}),BDP(C{row.name + 4}&" Corp", "YAS_BOND_YLD", "YAS_YLD_FLAG=2", "YAS_BOND_PX", $L{row.name + 4}))', axis=1)
            # trade_record_df['加权收益率(%) - 市价估值'] = trade_record_df.apply(lambda row: f'=IF(OR(AND(BDP(C{row.name + 4}&" Corp", "BAIL_IN_BOND_DESIGNATION")="Y", BDP(C{row.name + 4}&" Corp", "TLAC_MREL_DESIGNATION")="TLAC", BDP(C{row.name + 4}&" Corp", "BASEL_III_DESIGNATION")=""), BDP(C{row.name + 4}&" Corp", "BASEL_III_DESIGNATION")="Tier 2", BDP(C{row.name + 4}&" Corp", "BASEL_III_DESIGNATION")="Additional Tier 1"), BDP(C{row.name + 4}&" Corp", "YAS_BOND_YLD", "YAS_YLD_FLAG=2", "YAS_BOND_PX", $L{row.name + 4}), BDP(C{row.name + 4}&" Corp", "YAS_BOND_YLD", "YAS_YLD_FLAG=15", "YAS_BOND_PX", $L{row.name + 4}))', axis=1)
            trade_record_df['加权收益率(%) - 市价估值'] = trade_record_df.apply(lambda row: f'=IF(OR(AND(T{row.name + 4}="TLAC", V{row.name + 4}="Y"), U{row.name + 4}="Tier 2", U{row.name + 4}="Additional Tier 1"), BDP(C{row.name + 4}&" Corp", "YAS_BOND_YLD", "YAS_YLD_FLAG=2", "YAS_BOND_PX", $L{row.name + 4}), BDP(C{row.name + 4}&" Corp", "YAS_BOND_YLD", "YAS_YLD_FLAG=15", "YAS_BOND_PX", $L{row.name + 4}))', axis=1)
            trade_record_df['久期'] = trade_record_df.apply(lambda row: f'=BDP($C{row.name + 4} & " CORP","DUR_MID")', axis=1)
            trade_record_df['涉险国家'] = trade_record_df.apply(lambda row: f'=BDP($C{row.name + 4} & " CORP","CNTRY_OF_RISK")', axis=1)
            trade_record_df['name_chinese_simplified'] = trade_record_df.apply(lambda row: f'=BDP(E{row.name + 4}, "name_chinese_simplified")', axis=1)
            trade_record_df['is_perpetual'] = trade_record_df.apply(lambda row: f'=BDP(E{row.name + 4}, "is_perpetual")', axis=1)
            trade_record_df['nxt_call_dt'] = trade_record_df.apply(lambda row: f'=BDP(E{row.name + 4}, "nxt_call_dt")', axis=1)
            # trade_record_df['bail_in_bond_designation'] = trade_record_df.apply(lambda row: f'=@BDP($C{row.name + 4} & " CORP", "BAIL_IN_BOND_DESIGNATION")', axis=1)
            trade_record_df['tlac_mrel_designation'] = trade_record_df.apply(lambda row: f'=BDP($E{row.name + 4}, "TLAC_MREL_DESIGNATION")', axis=1)
            trade_record_df['basel_iii_designation'] = trade_record_df.apply(lambda row: f'=BDP($E{row.name + 4}, "BASEL_III_DESIGNATION")', axis=1)
            trade_record_df['callable'] = trade_record_df.apply(lambda row: f'=BDP($E{row.name + 4}, "callable")', axis=1)
            trade_record_df['issuer'] = trade_record_df.apply(lambda row: f'=IF(BError(BDP($C{row.name + 4}&" ISIN","ISSUER"))="N.A.","",BError(BDP($C{row.name + 4}&" ISIN","ISSUER")))', axis=1)
            trade_record_df['cntry_issue_iso'] = trade_record_df.apply(lambda row: f'=IF(BError(BDP($C{row.name + 4}&" ISIN","CNTRY_ISSUE_ISO"))="N.A.","",BError(BDP($C{row.name + 4}&" ISIN","CNTRY_ISSUE_ISO")))', axis=1)
            trade_record_df['guarantor_name'] = trade_record_df.apply(lambda row: f'=IF(BError(BDP($C{row.name + 4}&" ISIN","GUARANTOR_NAME"))="N.A.","",BError(BDP($C{row.name + 4}&" ISIN","GUARANTOR_NAME")))', axis=1)
            trade_record_df['iso_country_guarantor'] = trade_record_df.apply(lambda row: f'=IF(BError(BDP($C{row.name + 4}&" ISIN","ISO_COUNTRY_GUARANTOR"))="N.A.","",BError(BDP($C{row.name + 4}&" ISIN","ISO_COUNTRY_GUARANTOR")))', axis=1)
            trade_record_df['keep_well_name'] = trade_record_df.apply(lambda row: f'=IF(BError(BDP(BError(BDS($C{row.name + 4}&" ISIN","KEEPWELL_AGREEMENT_BBID_LIST"))&" COMPANYID","NAME"))="N.A.","",BError(BDP(BError(BDS($C{row.name + 4}&" ISIN","KEEPWELL_AGREEMENT_BBID_LIST"))&" COMPANYID","NAME")))', axis=1)
            trade_record_df['keep_well_country_iso'] = trade_record_df.apply(lambda row: f'=IF(BError(BDP(BError(BDS($C{row.name + 4}&" ISIN","KEEPWELL_AGREEMENT_BBID_LIST"))&" COMPANYID","COUNTRY_ISO"))="N.A.","",BError(BDP(BError(BDS($C{row.name + 4}&" ISIN","KEEPWELL_AGREEMENT_BBID_LIST"))&" COMPANYID","COUNTRY_ISO")))', axis=1)
            trade_record_df['standby_loc_name'] = trade_record_df.apply(lambda row: f'=IF(BError(BDP(BError(BDS($C{row.name + 4}&" ISIN","STANDBY_LOC_BBID_LIST"))&" COMPANYID","NAME"))="N.A.","",BError(BDP(BError(BDS($C{row.name + 4}&" ISIN","STANDBY_LOC_BBID_LIST"))&" COMPANYID","NAME")))', axis=1)
            trade_record_df['standby_loc_country_iso'] = trade_record_df.apply(lambda row: f'=IF(BError(BDP(BError(BDS($C{row.name + 4}&" ISIN","STANDBY_LOC_BBID_LIST"))&" COMPANYID","COUNTRY_ISO"))="N.A.","",BError(BDP(BError(BDS($C{row.name + 4}&" ISIN","STANDBY_LOC_BBID_LIST"))&" COMPANYID","COUNTRY_ISO")))', axis=1)
            trade_record_df['industry_group'] = trade_record_df.apply(lambda row: f'=IF(BError(BDP($C{row.name + 4}&" CORP","INDUSTRY_GROUP"))="N.A.","",BError(BDP($C{row.name + 4}&" CORP","INDUSTRY_GROUP")))', axis=1)

    sql = "select settle_date, trade_ticket, isin_code, trade_amount from bond_repo_trade_record where fund_name = '" + fund_name + "' and as_date <= '" + report_date + "' order by settle_date"
    repo_trade_record_df = db.query(sql)
    if not repo_trade_record_df.empty:
        for df_index, df_row in repo_trade_record_df.iterrows():
            repo_trade_record_df['next_coupon_settle_date_rt'] = repo_trade_record_df.apply(lambda row: f'=BDP($C{row.name + 4} & " CORP", "next_coupon_settle_date_rt")', axis=1)

    sql = "select settle_date, trade_ticket, isin_code, trade_amount from bond_trade_record_mbs where fund_name = '" + fund_name + "' and as_date <= '" + report_date + "' order by settle_date"
    trade_record_mbs_df = db.query(sql)
    if not trade_record_mbs_df.empty:
        for df_index, df_row in trade_record_mbs_df.iterrows():
            trade_record_mbs_df['pm'] = trade_record_mbs_df['isin_code'] + ' @BVal Mtge'
            trade_record_mbs_df['name_chinese_traditional'] = 'The Government National Mortgage Association'
            trade_record_mbs_df['security_des'] = trade_record_mbs_df.apply(lambda row: f'=BDP(E{row.name + 4}, "security_des")', axis=1)
            trade_record_mbs_df['cpn'] = trade_record_mbs_df.apply(lambda row: f'=BDP(E{row.name + 4}, "cpn")', axis=1)
            trade_record_mbs_df['maturity'] = trade_record_mbs_df.apply(lambda row: f'=BDP(E{row.name + 4}, "maturity")', axis=1)
            trade_record_mbs_df['mtg_factor'] = trade_record_mbs_df.apply(lambda row: f'=IF($E$2<BDP(C{row.name + 4} & " MTGE","MTG_FACTOR_DT"), BDP(C{row.name + 4} & " MTGE","mtg_factor"), BDH(C{row.name + 4} & " MTGE","mtg_factor",$E$2,$E$2))', axis=1)
            trade_record_mbs_df['accrued_interest'] = trade_record_mbs_df.apply(lambda row: f'=BDP($C{row.name + 4} & " MTGE","TKT_ACCRUED","SETTLE_DT",TEXT(MAX($E$2+1,$A{row.name + 4}),"YYYYMMDD"),"BQ_FACE_AMT",$D{row.name + 4})', axis=1)
            trade_record_mbs_df['ai_coupon_date'] = trade_record_mbs_df.apply(lambda row: f'=IF($K{row.name + 4}=0, BDP($C{row.name + 4} & " MTGE","TKT_ACCRUED","SETTLE_DT",TEXT(MAX($E$2,$A{row.name + 4}),"YYYYMMDD"),"BQ_FACE_AMT",$D{row.name + 4}), 0)', axis=1)
            trade_record_mbs_df['mtg_gen_ticker'] = trade_record_mbs_df.apply(lambda row: f'=BDP($C{row.name + 4} & " MTGE", "mtg_gen_ticker")', axis=1)
            trade_record_mbs_df['px_mid'] = trade_record_mbs_df.apply(lambda row: f'=IF($E$2=TODAY(), BDP($E{row.name + 4}, "px_mid"), BDH($E{row.name + 4}, "px_mid", $E$2))', axis=1)
            trade_record_mbs_df['int_acc'] = trade_record_mbs_df.apply(lambda row: f'=BDP($C{row.name + 4} & " ISIN", "int_acc", "SETTLE_DT",$E$2)', axis=1)
            trade_record_mbs_df['加权收益率(%) - 市价估值'] = trade_record_mbs_df.apply(lambda row: f'=BDP($C{row.name + 4} & " MTGE","YLD_CNV_MID")', axis=1)
            trade_record_mbs_df['久期'] = trade_record_mbs_df.apply(lambda row: f'=BDP($C{row.name + 4} & " MTGE","DUR_MID")', axis=1)
            trade_record_mbs_df['涉险国家'] = trade_record_mbs_df.apply(lambda row: f'=BDP($C{row.name + 4} & " MTGE","CNTRY_OF_RISK")', axis=1)
            trade_record_mbs_df['issuer'] = trade_record_mbs_df.apply(lambda row: f'=IF(BError(BDP($C{row.name + 4}&" ISIN","ISSUER"))="N.A.","",BError(BDP($C{row.name + 4}&" ISIN","ISSUER")))', axis=1)
            trade_record_mbs_df['cntry_issue_iso'] = trade_record_mbs_df.apply(lambda row: f'=IF(BError(BDP($C{row.name + 4}&" ISIN","CNTRY_ISSUE_ISO"))="N.A.","",BError(BDP($C{row.name + 4}&" ISIN","CNTRY_ISSUE_ISO")))', axis=1)
            trade_record_mbs_df['guarantor_name'] = trade_record_mbs_df.apply(lambda row: f'=IF(BError(BDP($C{row.name + 4}&" ISIN","GUARANTOR_NAME"))="N.A.","",BError(BDP($C{row.name + 4}&" ISIN","GUARANTOR_NAME")))', axis=1)
            trade_record_mbs_df['iso_country_guarantor'] = trade_record_mbs_df.apply(lambda row: f'=IF(BError(BDP($C{row.name + 4}&" ISIN","ISO_COUNTRY_GUARANTOR"))="N.A.","",BError(BDP($C{row.name + 4}&" ISIN","ISO_COUNTRY_GUARANTOR")))', axis=1)
            trade_record_mbs_df['keep_well_name'] = trade_record_mbs_df.apply(lambda row: f'=IF(BError(BDP(BError(BDS($C{row.name + 4}&" ISIN","STANDBY_LOC_BBID_LIST"))&" COMPANYID","NAME"))="N.A.","",BError(BDP(BError(BDS($C{row.name + 4}&" ISIN","STANDBY_LOC_BBID_LIST"))&" COMPANYID","NAME")))', axis=1)
            trade_record_mbs_df['keep_well_country_iso'] = trade_record_mbs_df.apply(lambda row: f'=IF(BError(BDP(BError(BDS($C{row.name + 4}&" ISIN","KEEPWELL_AGREEMENT_BBID_LIST"))&" COMPANYID","COUNTRY_ISO"))="N.A.","",BError(BDP(BError(BDS($C{row.name + 4}&" ISIN","KEEPWELL_AGREEMENT_BBID_LIST"))&" COMPANYID","COUNTRY_ISO")))', axis=1)
            trade_record_mbs_df['standby_loc_name'] = trade_record_mbs_df.apply(lambda row: f'=IF(BError(BDP(BError(BDS($C{row.name + 4}&" ISIN","STANDBY_LOC_BBID_LIST"))&" COMPANYID","NAME"))="N.A.","",BError(BDP(BError(BDS($C{row.name + 4}&" ISIN","STANDBY_LOC_BBID_LIST"))&" COMPANYID","NAME")))', axis=1)
            trade_record_mbs_df['standby_loc_country_iso'] = trade_record_mbs_df.apply(lambda row: f'=IF(BError(BDP(BError(BDS($C{row.name + 4}&" ISIN","STANDBY_LOC_BBID_LIST"))&" COMPANYID","COUNTRY_ISO"))="N.A.","",BError(BDP(BError(BDS($C{row.name + 4}&" ISIN","STANDBY_LOC_BBID_LIST"))&" COMPANYID","COUNTRY_ISO")))', axis=1)
            trade_record_mbs_df['industry_group'] = trade_record_mbs_df.apply(lambda row: f'=IF(BError(BDP($C{row.name + 4}&" CORP","INDUSTRY_GROUP"))="N.A.","",BError(BDP($C{row.name + 4}&" CORP","INDUSTRY_GROUP")))', axis=1)

    with xlw.App(visible=False) as app:
        # 打开工作簿
        wb = xlw.Book(bba_data_macro_app_path)
        app = wb.app

        app.screen_updating = False
        app.display_alerts = False
        app.calculation = 'manual'

        # 将 df1 写入第一个 Sheet 的指定位置（例如 A1）
        sheet1 = wb.sheets['trade_record']  # 替换为你的 Sheet 名称
        sheet1.range('A4:AZ10000').clear_contents()
        sheet1.range('A4').options(index=False, header=False).value = trade_record_df
        sheet1.range('E2').value = report_date

        # 将 df2 写入第二个 Sheet 的指定位置（例如 B2）
        sheet2 = wb.sheets['repo_trade_record']  # 替换为你的 Sheet 名称
        sheet2.range('A4:AZ10000').clear_contents()
        sheet2.range('A4').options(index=False, header=False).value = repo_trade_record_df
        sheet2.range('E2').value = report_date

        # 将 df3 写入第三个 Sheet 的指定位置（例如 C3）
        sheet3 = wb.sheets['trade_record_mbs']  # 替换为你的 Sheet 名称
        sheet3.range('A4:AZ10000').clear_contents()
        sheet3.range('A4').options(index=False, header=False).value = trade_record_mbs_df
        sheet3.range('E2').value = report_date

        sheet1.activate()

        app.screen_updating = True
        app.display_alerts = True
        app.calculation = 'automatic'

        # 保存并关闭工作簿
        wb.save()
        wb.close()

    print("All transaction data has been stored in the database, and the xlsm file for obtaining BBA data has been set up!")

    input("Please press RETURN to exit...")  # 确保始终暂停
