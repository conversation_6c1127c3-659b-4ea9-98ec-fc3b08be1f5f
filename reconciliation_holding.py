import pandas as pd

from PublicFunc import *
from PublicFunc import Utility

# 显示所有列
pd.set_option('display.max_columns', None)

# 或者如果你想要设置最大列数为某个特定值而不是无限制，可以这样做：
# pd.set_option('display.max_columns', 100)  # 例如显示最多100列

# 显示完整的DataFrame内容，不截断
pd.set_option('display.expand_frame_repr', False)

# 如果你还想确保所有的行都显示出来，而不仅仅是前几行和后几行，你可以这样设置：
pd.set_option('display.max_rows', None)

warnings.filterwarnings("ignore", category=UserWarning, module="openpyxl")


if __name__ == '__main__':
    while True:
        user_input = input("请输入持仓比较日期(YYYY-MM-DD): ")
        if not user_input:  # 如果用户没有输入任何内容
            print("未输入日期，默认使用当前日期{}".format(dt.datetime.today().strftime('%Y-%m-%d')))
            recon_date = dt.datetime.today().strftime('%Y-%m-%d')
            break
        if is_valid_date(user_input):
            recon_date = user_input
            break
        else:
            print("无效的日期格式，请重新输入。")

    bbg_pm_file = find_latest_file(bbg_pm_holding_path, bbg_pm_filename_regex)
    cmbc_file = find_latest_file(cmbc_holding_path, cmbc_filename_regex)
    sc_repo_file = find_latest_file(sc_repo_holding_path, sc_repo_filename_regex)
    jpm_repo_file = find_latest_file(jpm_repo_holding_path, jpm_repo_filename_regex)

    bbg_pm_date = extract_date(bbg_pm_file)
    cmbc_date = extract_date(cmbc_file)
    sc_repo_date = extract_date(sc_repo_file)
    jpm_repo_date = extract_date(jpm_repo_file)

    bbg_pm_df = pd.read_excel(bbg_pm_holding_path + bbg_pm_file, index_col=None, parse_dates=True, header=0, skiprows=0)
    # bbg_pm_df.dropna(subset=['ISIN'], inplace=True)
    cmbc_df = pd.read_excel(cmbc_holding_path + cmbc_file, engine='openpyxl', index_col=None, parse_dates=True, header=0, skiprows=0)
    cmbc_df.dropna(subset=['Instrument'], inplace=True)
    sc_repo_df = pd.read_csv(sc_repo_holding_path + sc_repo_file, index_col=None, parse_dates=True, header=0, skiprows=0, skipinitialspace=True)
    sc_repo_df.dropna(subset=['ISIN'], inplace=True)
    # jpm_repo_df = pd.read_excel(jpm_repo_holding_path + jpm_repo_file, index_col=None, parse_dates=True, header=0, skiprows=0)

    wb = xlw.Book(jpm_repo_holding_path + jpm_repo_file, password=jpm_repo_file_password)
    sheet = wb.sheets[0]
    max_row = sheet.used_range.last_cell.row
    max_col = sheet.used_range.last_cell.column
    jpm_repo_df = sheet.range((10, 1), (max_row, max_col)).options(pd.DataFrame, index=False, header=True).value
    jpm_repo_df.dropna(subset=['ISIN'], inplace=True)
    wb.close()

    with xlw.App(visible=True) as app:
        # 创建一个新的工作簿
        wb = app.books[0]

        bbg_pm_sheet = wb.sheets[0]
        bbg_pm_sheet.range('A1').value = [bbg_pm_df.columns.tolist()] + bbg_pm_df.values.tolist()  # 写入列名 + 数据
        bbg_pm_sheet.name = 'bbg_pm_' + bbg_pm_date
        bbg_pm_sheet.used_range.font.name = "Calibri"
        bbg_pm_sheet.used_range.font.size = 11
        bbg_pm_sheet.autofit()

        cmbc_sheet = wb.sheets.add(name='cmbc_' + cmbc_date)
        cmbc_sheet.range('A1').value = [cmbc_df.columns.tolist()] + cmbc_df.values.tolist()  # 写入列名 + 数据
        cmbc_sheet.used_range.font.name = "Calibri"
        cmbc_sheet.used_range.font.size = 11
        cmbc_sheet.autofit()

        sc_repo_sheet = wb.sheets.add(name='sc_repo_' + sc_repo_date)
        sc_repo_sheet.range('A1').value = [sc_repo_df.columns.tolist()] + sc_repo_df.values.tolist()  # 写入列名 + 数据
        sc_repo_sheet.used_range.font.name = "Calibri"
        sc_repo_sheet.used_range.font.size = 11
        sc_repo_sheet.autofit()

        jpm_repo_sheet = wb.sheets.add(name='jpm_repo_' + jpm_repo_date)
        jpm_repo_sheet.range('A1').value = [jpm_repo_df.columns.tolist()] + jpm_repo_df.values.tolist()  # 写入列名 + 数据
        jpm_repo_sheet.used_range.font.name = "Calibri"
        jpm_repo_sheet.used_range.font.size = 11
        jpm_repo_sheet.autofit()

        sheet_recon_repo = wb.sheets.add(name='recon_repo')
        sheet_recon_holding = wb.sheets.add(name='recon_holdng')

        # 找到 "Repo Liability" 行的索引
        repo_start_idx = bbg_pm_df[bbg_pm_df.columns[0]].eq('Repo Liability').idxmax() + 1
        # 初始化结束索引
        repo_end_idx = repo_start_idx + 1
        # 找到连续为空的行的结束索引
        while repo_end_idx < len(bbg_pm_df) and pd.isna(bbg_pm_df.iat[repo_end_idx, 0]):
            repo_end_idx += 1
        # 提取指定列的数据
        bbg_repo_data = bbg_pm_df.iloc[repo_start_idx:repo_end_idx, bbg_pm_df.columns.get_indexer(['ISIN', 'Settled Position'])]
        bbg_repo_data = bbg_repo_data.rename(columns={'ISIN': 'isin_AIMS', 'Settled Position': 'quantity_held_AIMS'})

        # 复制jpm_repo和sc_repo的记录
        jpm_repo_data = jpm_repo_df[['ISIN', 'Security Des', 'Face', 'Cash']]
        jpm_repo_data = jpm_repo_data.rename(columns={'ISIN': 'isin_JPM', 'Security Des': 'security_des', 'Face': 'quantity_held_JPM', 'Cash': 'cash_held_JPM'})
        # sc_repo_df.columns.str.strip()
        # sc_repo_df.rename(columns=lambda x: x.strip(), inplace=True)
        sc_repo_data = sc_repo_df[['ISIN', 'Notional', 'Start Cash']].copy()
        sc_repo_data['security_des'] = ''
        sc_repo_data = sc_repo_data.rename(columns={'ISIN': 'isin_SC', 'Notional': 'quantity_held_SC', 'Start Cash': 'cash_held_SC'})
        sc_repo_data = sc_repo_data[['isin_SC', 'security_des', 'quantity_held_SC', 'cash_held_SC']]

        sheet_recon_repo.range('A1').value = [jpm_repo_data.columns.tolist()] + jpm_repo_data.values.tolist()
        sheet_recon_repo.range('A' + str(len(jpm_repo_data) + 4)).value = [sc_repo_data.columns.tolist()] + sc_repo_data.values.tolist()

        sheet_recon_repo.range('G1').value = 'Date'
        sheet_recon_repo.range('H1').value = recon_date
        sheet_recon_repo.range('H3').value = 'No of Repo'
        sheet_recon_repo.range('I3').value = 'SUM'
        sheet_recon_repo.range('G4').value = 'AIMS'
        sheet_recon_repo.range('H4').value = '=COUNTIF(' + bbg_pm_sheet.name + '!D' + str(repo_start_idx) + ':D' + str(repo_end_idx) + ',"<>")'
        sheet_recon_repo.range('I4').value = '=' + bbg_pm_sheet.name + '!W3'
        sheet_recon_repo.range('G5').value = 'JPM'
        sheet_recon_repo.range('H5').value = '=COUNTIF(' + jpm_repo_sheet.name + '!D:D,"<>")-1'
        sheet_recon_repo.range('I5').value = '=SUM(' + jpm_repo_sheet.name + '!L:L)'
        sheet_recon_repo.range('G6').value = 'SC'
        sheet_recon_repo.range('H6').value = '=COUNTIF(' + sc_repo_sheet.name + '!A:A,"<>")-1'
        sheet_recon_repo.range('I6').value = '=-SUM(' + sc_repo_sheet.name + '!L:L)'
        sheet_recon_repo.range('G8').value = 'DIFF'
        sheet_recon_repo.range('H8').value = '=H4-H5-H6'
        sheet_recon_repo.range('I8').value = '=I4-I5-I6'

        sheet_recon_repo.used_range.font.name = "Calibri"
        sheet_recon_repo.used_range.font.size = 11
        sheet_recon_repo.range('C:D').number_format = '#,##0.000'
        sheet_recon_repo.range('H4:H10').number_format = '#,##0'
        sheet_recon_repo.range('I4:I10').number_format = '_(* #,##0.00_);_(* (#,##0.00);_(* "-"??_);_(@_)'

        sheet_recon_repo.autofit()

        # 找到 "government bond" 行的索引
        start_idx = bbg_pm_df[bbg_pm_df.columns[0]].eq('Government Bond').idxmax() + 1
        # 初始化结束索引
        end_idx = start_idx + 1
        # 找到连续为空的行的结束索引
        while end_idx < len(bbg_pm_df) and pd.isna(bbg_pm_df.iat[end_idx, 0]):
            end_idx += 1
        # 提取指定列的数据
        extracted_data = bbg_pm_df.iloc[start_idx:end_idx, bbg_pm_df.columns.get_indexer(['ISIN', 'Settled Position'])]

        # 找到 "Treasury" 行的索引
        start_idx = bbg_pm_df[bbg_pm_df.columns[0]].eq('Treasury').idxmax() + 1
        # 初始化结束索引
        end_idx = start_idx + 1
        # 找到连续为空的行的结束索引
        while end_idx < len(bbg_pm_df) and pd.isna(bbg_pm_df.iat[end_idx, 0]):
            end_idx += 1
        # 提取指定列的数据
        extracted_data = pd.concat([extracted_data, bbg_pm_df.iloc[start_idx:end_idx, bbg_pm_df.columns.get_indexer(['ISIN', 'Settled Position'])]], axis=0).reset_index(drop=True)

        # 找到 "Corporate Bond" 行的索引
        start_idx = bbg_pm_df[bbg_pm_df.columns[0]].eq('Corporate Bond').idxmax() + 1
        # 初始化结束索引
        end_idx = start_idx + 1
        # 找到连续为空的行的结束索引
        while end_idx < len(bbg_pm_df) and pd.isna(bbg_pm_df.iat[end_idx, 0]):
            end_idx += 1
        # 提取指定列的数据
        extracted_data = pd.concat([extracted_data, bbg_pm_df.iloc[start_idx:end_idx, bbg_pm_df.columns.get_indexer(['ISIN', 'Settled Position'])]], axis=0).reset_index(drop=True)

        extracted_data = extracted_data.rename(columns={'ISIN': 'isin_AIMS', 'Settled Position': 'quantity_held_AIMS'})
        recon_holding_df = extracted_data.copy()

        offset = 2
        extracted_data = cmbc_df[['Instrument', 'Available Qty']]
        extracted_data = extracted_data.rename(columns={'Instrument': 'isin_CMBC', 'Available Qty': 'quantity_held_CMBC'})
        merged_df = pd.merge(recon_holding_df, extracted_data, how='outer', left_on='isin_AIMS', right_on='isin_CMBC')
        merged_df = merged_df[['isin_AIMS', 'isin_CMBC', 'quantity_held_AIMS', 'quantity_held_CMBC']]
        merged_df['quantity_held_AIMS'] = merged_df['quantity_held_AIMS'] * 1000
        merged_df['BBG_vs_CMBC(Cust)'] = merged_df.apply(lambda row: f'=C{row.name + offset}=D{row.name + offset}', axis=1)
        merged_df['Diff'] = merged_df.apply(lambda row: f'=IF(A{row.name + offset}<>"",IF($D{row.name + offset}-$C{row.name + offset}=0,"",$D{row.name + offset}-$C{row.name + offset}),"")', axis=1)
        merged_df['Remark'] = merged_df.apply(lambda row: f'=IF(A{row.name + offset}<>"",IFNA(IF(VLOOKUP(A{row.name + offset},''recon_repo''!A:C,3,0)<>0,"REPO"),""), "")', axis=1)

        recon_holding_df = merged_df.copy()

        sheet_recon_holding.range('A1').value = [recon_holding_df.columns.tolist()] + recon_holding_df.values.tolist()
        sheet_recon_holding.range('I1').value = 'Date'
        sheet_recon_holding.range('J1').value = recon_date
        sheet_recon_holding.range('I2').value = 'AIMS'
        sheet_recon_holding.range('J2').value = '=COUNTIF(A:A,"<>")'
        sheet_recon_holding.range('I3').value = 'CMBC'
        sheet_recon_holding.range('J3').value = '=COUNTIF(B:B,"<>")'

        sheet_recon_holding.used_range.font.name = "Calibri"
        sheet_recon_holding.used_range.font.size = 11
        sheet_recon_holding.range('C:D').number_format = '#,##0.000'
        sheet_recon_holding.range('F:F').number_format = '_(* #,##0.00_);_(* (#,##0.00);_(* "-"??_);_(@_)'

        sheet_recon_holding.autofit()

        reconciliation_report_path = reconciliation_report_path + 'reconciliation_report—ZOAMI - ' + fund_name + '_' + recon_date + '.xlsx'
        wb.save(reconciliation_report_path)

        # 关闭工作簿
        # wb.close()
        # app.quit()

        print('Reconciliation Report Created Successfully')
        print('----------------------------------------')
        input('请按回车键退出...')

