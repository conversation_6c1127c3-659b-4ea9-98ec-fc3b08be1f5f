.\venv\Scripts\pyinstaller -F --hidden-import platformdirs --hidden-import pandas --hidden-import pyodbc --hidden-import openpyxl --hidden-import _ctypes --hidden-import="pandas._libs.tslibs.timedeltas" --hidden-import="pandas._libs.tslibs.np_datetime" --hidden-import="pandas._libs.tslibs.nattype" --hidden-import="pandas._libs.skiplist" --add-data "config.ini;." --add-data ".\dll\*.dll:bin" --add-binary ".\dll\sqlite3.dll;." generate_valuation_report.py


.\venv\Scripts\pyinstaller -F --hidden-import platformdirs --hidden-import pandas --hidden-import pyodbc --hidden-import openpyxl --hidden-import _ctypes --hidden-import="pandas._libs.tslibs.timedeltas" --hidden-import="pandas._libs.tslibs.np_datetime" --hidden-import="pandas._libs.tslibs.nattype" --hidden-import="pandas._libs.skiplist" --add-data "config.ini;." --add-data ".\dll\*.dll:bin" --add-binary ".\dll\sqlite3.dll;." import_trade_record.py